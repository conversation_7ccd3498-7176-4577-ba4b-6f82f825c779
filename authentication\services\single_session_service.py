import logging
import uuid
from datetime import datetime, timedelta, timezone as dt_timezone
from django.db import connection, transaction
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.settings import api_settings

from authentication.utils import get_token_storage_hash

logger = logging.getLogger(__name__)


class SingleSessionService:
    """
    Service to handle single-session security policy for JWT tokens.
    Ensures only one active session per user by managing tokens in tcd_outstanding_tokens.
    """
    
    @staticmethod
    def invalidate_existing_user_tokens(user_id, user_type):
        """
        Invalidate all existing tokens for a specific user by blacklisting them.
        This ensures single-session policy where only one device can be logged in at a time.
        
        Args:
            user_id (int): User ID
            user_type (str): User type ('member', 'consultant', 'staff')
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Invalidating existing tokens for {user_type} user {user_id}")
            
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # Find all active tokens for this user
                    cursor.execute("""
                        SELECT ot.id, ot.jti
                        FROM tcd_outstanding_tokens ot
                        LEFT JOIN tcd_blacklisted_tokens bt ON ot.id = bt.token_id
                        WHERE ot.user_id = %s 
                          AND ot.user_type = %s 
                          AND ot.expires_at > %s
                          AND bt.id IS NULL
                    """, [user_id, user_type, timezone.now()])
                    
                    existing_tokens = cursor.fetchall()
                    
                    if not existing_tokens:
                        logger.info(f"No existing active tokens found for {user_type} user {user_id}")
                        return True
                    
                    logger.info(f"Found {len(existing_tokens)} active tokens to invalidate for {user_type} user {user_id}")
                    
                    # Blacklist all existing tokens
                    blacklist_time = timezone.now()
                    for token_id, jti in existing_tokens:
                        cursor.execute("""
                            INSERT INTO tcd_blacklisted_tokens (token_id, blacklisted_at)
                            VALUES (%s, %s)
                        """, [token_id, blacklist_time])
                        
                        logger.info(f"Blacklisted token {jti} for {user_type} user {user_id}")
                    
                    logger.info(f"Successfully invalidated {len(existing_tokens)} tokens for {user_type} user {user_id}")
                    return True
                    
        except Exception as e:
            logger.error(f"Error invalidating existing tokens for {user_type} user {user_id}: {str(e)}")
            return False
    
    @staticmethod
    def store_token_in_outstanding(token_str, user_id, user_type):
        """
        Store a JWT token in the tcd_outstanding_tokens table with proper hashing.
        
        Args:
            token_str (str): JWT token string
            user_id (int): User ID
            user_type (str): User type ('member', 'consultant', 'staff')
            
        Returns:
            dict: {'success': bool, 'token_id': int or None, 'error': str or None}
        """
        try:
            from rest_framework_simplejwt.tokens import UntypedToken
            
            # Decode token to get JTI and expiration
            try:
                untyped_token = UntypedToken(token_str)
                jti = untyped_token.get(api_settings.JTI_CLAIM)
                exp = untyped_token.get('exp')

                if exp:
                    expires_at = datetime.fromtimestamp(exp, tz=dt_timezone.utc)
                else:
                    # Default expiration if not found in token
                    expires_at = timezone.now() + timedelta(days=1)
                    
            except Exception as e:
                logger.error(f"Error decoding token: {str(e)}")
                return {'success': False, 'token_id': None, 'error': f'Invalid token: {str(e)}'}
            
            # Format JTI for database storage
            jti_formatted = SingleSessionService._format_jti_for_database(jti)
            
            # Hash the token for secure storage
            token_hash = get_token_storage_hash(token_str)
            
            with transaction.atomic():
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO tcd_outstanding_tokens
                        (jti, token, created_at, expires_at, user_id, user_type)
                        OUTPUT INSERTED.id
                        VALUES (CONVERT(UNIQUEIDENTIFIER, %s), %s, %s, %s, %s, %s)
                    """, [jti_formatted, token_hash, timezone.now(), expires_at, user_id, user_type])
                    
                    result = cursor.fetchone()
                    if not result or result[0] is None:
                        logger.error("Failed to get inserted token ID")
                        return {'success': False, 'token_id': None, 'error': 'Failed to store token'}
                    
                    token_id = int(result[0])
                    logger.info(f"Successfully stored token {jti_formatted} for {user_type} user {user_id} with ID {token_id}")
                    
                    return {'success': True, 'token_id': token_id, 'error': None}
                    
        except Exception as e:
            logger.error(f"Error storing token for {user_type} user {user_id}: {str(e)}")
            return {'success': False, 'token_id': None, 'error': str(e)}
    
    @staticmethod
    def verify_token_exists(token_str):
        """
        Verify that a token exists in the tcd_outstanding_tokens table and is not blacklisted.
        
        Args:
            token_str (str): JWT token string
            
        Returns:
            bool: True if token exists and is valid, False otherwise
        """
        try:
            from rest_framework_simplejwt.tokens import UntypedToken
            
            # Decode token to get JTI
            try:
                untyped_token = UntypedToken(token_str)
                jti = untyped_token.get(api_settings.JTI_CLAIM)
            except Exception as e:
                logger.error(f"Error decoding token for verification: {str(e)}")
                return False
            
            # Format JTI and hash token for database lookup
            jti_formatted = SingleSessionService._format_jti_for_database(jti)
            token_hash = get_token_storage_hash(token_str)
            
            with connection.cursor() as cursor:
                # Check if token exists and is not blacklisted
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM tcd_outstanding_tokens ot
                    LEFT JOIN tcd_blacklisted_tokens bt ON ot.id = bt.token_id
                    WHERE ot.jti = CONVERT(UNIQUEIDENTIFIER, %s) 
                      AND ot.token = %s
                      AND ot.expires_at > %s
                      AND bt.id IS NULL
                """, [jti_formatted, token_hash, timezone.now()])
                
                result = cursor.fetchone()
                is_valid = result[0] > 0 if result else False
                
                if is_valid:
                    logger.debug(f"Token {jti_formatted} is valid and exists in outstanding tokens")
                else:
                    logger.warning(f"Token {jti_formatted} is not valid or does not exist in outstanding tokens")
                
                return is_valid
                
        except Exception as e:
            logger.error(f"Error verifying token existence: {str(e)}")
            return False
    
    @staticmethod
    def _format_jti_for_database(jti):
        """
        Format JTI for database storage, converting hex strings to UUID format if needed.
        
        Args:
            jti (str): JWT ID
            
        Returns:
            str: Formatted JTI for database storage
        """
        if not jti:
            return str(uuid.uuid4())
        
        # Convert hex string to UUID format if needed
        if len(jti) == 32 and all(c in '0123456789abcdef' for c in jti.lower()):
            # Convert hex string to UUID format
            formatted_jti = f"{jti[:8]}-{jti[8:12]}-{jti[12:16]}-{jti[16:20]}-{jti[20:]}"
            return formatted_jti
        
        return str(jti)
    
    @staticmethod
    def implement_single_session_login(user_id, user_type, tokens):
        """
        Implement single-session login by invalidating existing tokens and storing new ones.
        
        Args:
            user_id (int): User ID
            user_type (str): User type ('member', 'consultant', 'staff')
            tokens (dict): Dictionary containing 'access' and 'refresh' tokens
            
        Returns:
            dict: {'success': bool, 'error': str or None}
        """
        try:
            logger.info(f"Implementing single-session login for {user_type} user {user_id}")
            
            # Step 1: Invalidate all existing tokens for this user
            if not SingleSessionService.invalidate_existing_user_tokens(user_id, user_type):
                logger.error(f"Failed to invalidate existing tokens for {user_type} user {user_id}")
                return {'success': False, 'error': 'Failed to invalidate existing tokens'}
            
            # Step 2: Store the new refresh token
            refresh_result = SingleSessionService.store_token_in_outstanding(
                tokens['refresh'], user_id, user_type
            )
            if not refresh_result['success']:
                logger.error(f"Failed to store refresh token: {refresh_result['error']}")
                return {'success': False, 'error': f"Failed to store refresh token: {refresh_result['error']}"}
            
            # Step 3: Store the new access token
            access_result = SingleSessionService.store_token_in_outstanding(
                tokens['access'], user_id, user_type
            )
            if not access_result['success']:
                logger.error(f"Failed to store access token: {access_result['error']}")
                return {'success': False, 'error': f"Failed to store access token: {access_result['error']}"}
            
            logger.info(f"Successfully implemented single-session login for {user_type} user {user_id}")
            return {'success': True, 'error': None}
            
        except Exception as e:
            logger.error(f"Error implementing single-session login for {user_type} user {user_id}: {str(e)}")
            return {'success': False, 'error': str(e)}
