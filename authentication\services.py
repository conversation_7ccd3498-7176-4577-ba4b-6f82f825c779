# -*- coding: utf-8 -*-
"""
Authentication Services
ปรับปรุง authentication services ให้ใช้ standard response format และ error codes
"""

from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from datetime import timedelta, date
import logging

from authentication.models import (
    TcdAppMember, 
    TcdUserConsult, 
    TcdAppLogs, 
    TcdUserConsultTeam, 
    TcdAppMasMemberType, 
    TcdAppMasGovernmentSector, 
    TcdAppMasMinistry, 
    TcdAppMasDepartment
)
from tracking.models import TcdWorklist
from MCDC.models import TcdCorporateType
from MCDC.models import TcdUsers
from authentication.utils import (
    verify_password, 
    validate_api_connection,
    validate_api_key,
    save_login_data,
    send_user_data,
    validate_data_transmission,
    prepare_user_display_data,
    save_login_record,
    get_client_ip,
    md5_hash,
)
from authentication.constants import (
    MemberStatus, 
    LoginAttempts, 
    ConsultantVerificationStatus,
    ConsultantType,
    LoginSessionStatus,
)

# Import standard response utilities
from utils.response import (
    APIResponse,
    get_language_from_request,
    service_success_response,
    service_error_response
)

# Import for app usage tracking
from authentication.models import TcdAppStatistic, TcdAppLogs, TcdActionLog

# Import OTP service
from authentication.services.otp_service import OTPService

# Setup logging
logger = logging.getLogger(__name__)


class MemberAuthService:
    """
    Service สำหรับ authentication ของ Member ที่ใช้ Standard Response Format
    """
    
    @staticmethod
    def check_identity_card_duplicate(identity_card_no, language='th'):
        """
        ตรวจสอบว่า identity_card_no มีซ้ำในตาราง TcdAppMember เท่านั้น
        
        Args:
            identity_card_no (str): หมายเลขบัตรประชาชนที่ต้องการตรวจสอบ
            language (str): ภาษา ('th' หรือ 'en')
            
        Returns:
            dict: {
                'success': bool,
                'data': dict หรือ {},
                'error_code': int หรือ None,
                'error_message': str หรือ None,
                'existing_member': TcdAppMember object หรือ None (เฉพาะกรณีมีซ้ำ)
            }
        """
        try:
            # ตรวจสอบว่า identity_card_no ไม่เป็นค่าว่าง
            if not identity_card_no or identity_card_no.strip() == '':
                return service_success_response(
                    data={
                        'is_duplicate': False,
                        'message': 'PID ว่างเปล่า ไม่ต้องตรวจสอบ'
                    },
                    language=language
                )
            
            # สร้าง query สำหรับค้นหา identity_card_no ที่ซ้ำในตาราง TcdAppMember เท่านั้น
            query = TcdAppMember.objects.filter(identity_card_no=identity_card_no.strip())

            # ตรวจสอบว่ามี member ที่ใช้ identity_card_no นี้อยู่แล้วหรือไม่
            existing_member = query.first()

            if existing_member:
                logger.warning(f"Duplicate identity_card_no found: {identity_card_no} for member ID: {existing_member.id}")
                
                # Return error response with additional data
                error_response = service_error_response(
                    error_code=2010,  # ข้อมูลซ้ำ
                    language=language
                )
                # เพิ่มข้อมูลเพิ่มเติม
                error_response['existing_member'] = existing_member
                error_response['data'] = {
                    'is_duplicate': True,
                    'existing_member_id': existing_member.id,
                    # 'existing_member_name': f"{existing_member.first_name} {existing_member.last_name}"
                }
                return error_response
            else:
                logger.info(f"No duplicate found for identity_card_no: {identity_card_no}")
                return service_success_response(
                    data={
                        'is_duplicate': False,
                        'message': 'PID นี้สามารถใช้งานได้'
                    },
                    language=language
                )
                
        except Exception as e:
            logger.error(f"Error checking identity_card_no duplicate: {str(e)}")
            return service_error_response(
                error_code=3000,  # เกิดข้อผิดพลาดในฐานข้อมูล
                language=language
            )
    
    @staticmethod
    def check_email_duplicate(email, language='th'):
        """
        ตรวจสอบว่า email ซ้ำหรือไม่ (รวมทั้ง primary และ backup email)

        Args:
            email (str): อีเมลที่ต้องการตรวจสอบ
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: {
                'success': bool,
                'data': dict หรือ {},
                'error_code': int หรือ None,
                'error_message': str หรือ None,
                'existing_member': TcdAppMember object หรือ None (เฉพาะกรณีมีซ้ำ)
            }
        """
        try:
            # ตรวจสอบว่า email ไม่เป็นค่าว่าง
            if not email or email.strip() == '':
                return service_success_response(
                    data={
                        'is_duplicate': False,
                        'message': 'อีเมลว่างเปล่า ไม่ต้องตรวจสอบ'
                    },
                    language=language
                )

            email = email.strip()

            # ตรวจสอบใน TcdAppMember (primary email)
            existing_member = TcdAppMember.objects.filter(email=email).first()
            if existing_member:
                logger.warning(f"Duplicate email found in TcdAppMember: {email} for member ID: {existing_member.id}")

                error_response = service_error_response(
                    error_code=2011,  # อีเมลซ้ำ
                    language=language
                )
                error_response['existing_member'] = existing_member
                error_response['data'] = {
                    'is_duplicate': True,
                    'existing_member_id': existing_member.id,
                    'duplicate_source': 'app_member.email'
                }
                return error_response

            # ตรวจสอบใน TcdUserConsult (primary email)
            existing_consultant = TcdUserConsult.objects.filter(email=email).first()
            if existing_consultant:
                logger.warning(f"Duplicate email found in TcdUserConsult.email: {email} for consultant ID: {existing_consultant.id}")

                error_response = service_error_response(
                    error_code=2011,  # อีเมลซ้ำ
                    language=language
                )
                error_response['existing_consultant'] = existing_consultant
                error_response['data'] = {
                    'is_duplicate': True,
                    'existing_consultant_id': existing_consultant.id,
                    'duplicate_source': 'user_consult.email'
                }
                return error_response

            # ตรวจสอบใน TcdUserConsult (backup email - email_second)
            existing_consultant_backup = TcdUserConsult.objects.filter(email_second=email).first()
            if existing_consultant_backup:
                logger.warning(f"Duplicate email found in TcdUserConsult.email_second: {email} for consultant ID: {existing_consultant_backup.id}")

                error_response = service_error_response(
                    error_code=2011,  # อีเมลซ้ำ
                    language=language
                )
                error_response['existing_consultant'] = existing_consultant_backup
                error_response['data'] = {
                    'is_duplicate': True,
                    'existing_consultant_id': existing_consultant_backup.id,
                    'duplicate_source': 'user_consult.email_second'
                }
                return error_response

            logger.info(f"No duplicate found for email: {email}")
            return service_success_response(
                data={
                    'is_duplicate': False,
                    'message': 'อีเมลนี้สามารถใช้งานได้'
                },
                language=language
            )

        except Exception as e:
            logger.error(f"Error checking email duplicate: {str(e)}")
            return service_error_response(
                error_code=3000,  # เกิดข้อผิดพลาดในฐานข้อมูล
                language=language
            )
    
    @staticmethod
    def check_phone_duplicate(phone, language='th'):
        """
        ตรวจสอบว่า phone ซ้ำหรือไม่ (รวมทั้ง primary และ backup phone)

        Args:
            phone (str): เบอร์โทรศัพท์ที่ต้องการตรวจสอบ
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: {
                'success': bool,
                'data': dict หรือ {},
                'error_code': int หรือ None,
                'error_message': str หรือ None,
                'existing_member': TcdAppMember object หรือ None (เฉพาะกรณีมีซ้ำ)
            }
        """
        try:
            # ตรวจสอบว่า phone ไม่เป็นค่าว่าง
            if not phone or phone.strip() == '':
                return service_success_response(
                    data={
                        'is_duplicate': False,
                        'message': 'เบอร์โทรศัพท์ว่างเปล่า ไม่ต้องตรวจสอบ'
                    },
                    language=language
                )

            # ทำความสะอาดเบอร์โทร (ลบช่องว่าง ขีด และวงเล็บ)
            cleaned_phone = phone.strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')

            # สร้าง patterns สำหรับการค้นหา
            patterns = [
                cleaned_phone,  # เบอร์ที่ทำความสะอาดแล้ว
                cleaned_phone[-9:] if len(cleaned_phone) > 9 else cleaned_phone,  # 9 ตัวสุดท้าย (ไม่มีรหัสประเทศ)
            ]

            from django.db.models import Q

            # ตรวจสอบใน TcdAppMember (primary phone)
            for pattern in patterns:
                if pattern:
                    existing_member = TcdAppMember.objects.filter(phone__icontains=pattern).first()
                    if existing_member:
                        logger.warning(f"Duplicate phone found in TcdAppMember: {phone} for member ID: {existing_member.id}")

                        error_response = service_error_response(
                            error_code=2012,  # เบอร์โทรศัพท์ซ้ำ
                            language=language
                        )
                        error_response['existing_member'] = existing_member
                        error_response['data'] = {
                            'is_duplicate': True,
                            'existing_member_id': existing_member.id,
                            'duplicate_source': 'app_member.phone'
                        }
                        return error_response

            # ตรวจสอบใน TcdUserConsult (primary phone)
            for pattern in patterns:
                if pattern:
                    existing_consultant = TcdUserConsult.objects.filter(phone__icontains=pattern).first()
                    if existing_consultant:
                        logger.warning(f"Duplicate phone found in TcdUserConsult.phone: {phone} for consultant ID: {existing_consultant.id}")

                        error_response = service_error_response(
                            error_code=2012,  # เบอร์โทรศัพท์ซ้ำ
                            language=language
                        )
                        error_response['existing_consultant'] = existing_consultant
                        error_response['data'] = {
                            'is_duplicate': True,
                            'existing_consultant_id': existing_consultant.id,
                            'duplicate_source': 'user_consult.phone'
                        }
                        return error_response

            # ตรวจสอบใน TcdUserConsult (backup phone - phone_second)
            for pattern in patterns:
                if pattern:
                    existing_consultant_backup = TcdUserConsult.objects.filter(phone_second__icontains=pattern).first()
                    if existing_consultant_backup:
                        logger.warning(f"Duplicate phone found in TcdUserConsult.phone_second: {phone} for consultant ID: {existing_consultant_backup.id}")

                        error_response = service_error_response(
                            error_code=2012,  # เบอร์โทรศัพท์ซ้ำ
                            language=language
                        )
                        error_response['existing_consultant'] = existing_consultant_backup
                        error_response['data'] = {
                            'is_duplicate': True,
                            'existing_consultant_id': existing_consultant_backup.id,
                            'duplicate_source': 'user_consult.phone_second'
                        }
                        return error_response

            logger.info(f"No duplicate found for phone: {phone}")
            return service_success_response(
                data={
                    'is_duplicate': False,
                    'message': 'เบอร์โทรศัพท์นี้สามารถใช้งานได้'
                },
                language=language
            )

        except Exception as e:
            logger.error(f"Error checking phone duplicate: {str(e)}")
            return service_error_response(
                error_code=3000,  # เกิดข้อผิดพลาดในฐานข้อมูล
                language=language
            )
    
    @staticmethod
    def authenticate(request, username, password, member=None):
        """
        ตรวจสอบการเข้าสู่ระบบของ Member ตาม Flowchart
        
        Args:
            request: Django request object
            username: ชื่อผู้ใช้
            password: รหัสผ่าน
            member: TcdAppMember object (optional) - ถ้ามีจะใช้แทนการค้นหาจากฐานข้อมูล
            
        Returns:
            dict: Standard response format
        """
        # Get language from request
        language = get_language_from_request(request)
        
        logger.info(f"Starting authentication for member: {username}")
        
        try:
            # ตรวจสอบ member ที่ส่งเข้ามา
            if member:
                # ถ้ามี member ที่ส่งมาจาก serializer ให้ตรวจสอบว่าบัญชีถูกล็อคหรือไม่
                now = timezone.now()
                if member.lockout_end_date and member.lockout_end_date > now:
                    time_remaining = member.lockout_end_date - now
                    logger.warning(f"ACCOUNT LOCKED: Member {username} is locked out until {member.lockout_end_date}")
                    logger.warning(f"ACCOUNT LOCKED: Remaining time: {time_remaining.total_seconds()} seconds")
                    
                    # บันทึก app logs เมื่อเกิด error_code 1002 (Account locked)
                    logger.warning(f"Failed login with error_code=1002: ACCOUNT LOCKED")
                    ActionLogService.log_account_locked_attempt(request, member, 1002, language)
                    
                    # ส่งค่า error_code 1002
                    return service_error_response(
                        error_code=1002,  # User account is locked
                        language=language
                    )
                
                # ถ้าไม่ถูกล็อค ให้ใช้ member นั้นต่อไป
                logger.info(f"Using provided member: {member.username}")
            else:
                # ถ้าไม่มี member ให้ตรวจสอบจากฐานข้อมูล
                try:
                    member = TcdAppMember.objects.get(username=username)
                    now = timezone.now()
                    
                    # ตรวจสอบว่าบัญชีถูกล็อคหรือไม่
                    if member.lockout_end_date and member.lockout_end_date > now:
                        time_remaining = member.lockout_end_date - now
                        logger.warning(f"ACCOUNT LOCKED CHECK: Member {username} is locked out until {member.lockout_end_date}")
                        logger.warning(f"ACCOUNT LOCKED CHECK: Remaining time: {time_remaining.total_seconds()} seconds")
                        
                        # บันทึก app logs เมื่อเกิด error_code 1002 (Account locked)
                        logger.warning(f"Failed login with error_code=1002: ACCOUNT LOCKED")
                        logger.warning(f"Account is locked: {username}, lockout_end_date: {member.lockout_end_date}")
                        ActionLogService.log_account_locked_attempt(request, member, 1002, language)
                        
                        # ส่งค่า error_code 1002
                        return service_error_response(
                            error_code=1002,  # User account is locked
                            language=language
                        )
                except TcdAppMember.DoesNotExist:
                    # ไม่พบบัญชี ใช้ error_code 1001
                    logger.warning(f"Member not found: {username}")
                    return service_error_response(
                        error_code=1001,  # Invalid username or password
                        language=language
                    )
                
                # Step 2: ตรวจสอบการเข้าสู่ระบบ
                logger.info("Step 2: Authenticating user credentials")
                auth_result = MemberAuthService._authenticate_credentials(username, password, language)
                if not auth_result['success']:
                    error_code = auth_result.get('error_code')
                    logger.error(f"Member authentication failed: {error_code}")
                    
                    # บันทึก action log เมื่อเกิด error_code 1001
                    if error_code == 1001:
                        logger.info(f"Failed login with error_code={error_code}: Invalid credentials")
                        ActionLogService.log_failed_login_attempt(request, username, 1001, language)
                    
                    return service_error_response(
                        error_code=error_code,
                        language=language
                    )
                
                member = auth_result['member']
            
            logger.info(f"Member authentication successful: {username}")
            
            # ตรวจสอบอีกรอบว่าบัญชีไม่ถูกล็อค
            now = timezone.now()
            if member.lockout_end_date and member.lockout_end_date > now:
                time_remaining = member.lockout_end_date - now
                logger.warning(f"FINAL CHECK: Member {username} is still locked out until {member.lockout_end_date}")
                logger.warning(f"FINAL CHECK: Remaining time: {time_remaining.total_seconds()} seconds")
                
                # บันทึก app logs เมื่อเกิด error_code 1002 (Account locked)
                logger.warning(f"Failed login with error_code=1002: ACCOUNT LOCKED (final check)")
                ActionLogService.log_account_locked_attempt(request, member, 1002, language)
                
                # ส่งค่า error_code 1002
                return service_error_response(
                    error_code=1002,  # User account is locked
                    language=language
                )
            
            # Step 3: สร้าง JWT tokens
            logger.info("Step 3: Generating JWT tokens")
            try:
                tokens = MemberAuthService.generate_tokens(member)
            except Exception as e:
                logger.error(f"Token generation failed: {str(e)}")
                return service_error_response(
                    error_code=1005,  # Invalid token / Token generation failed
                    language=language
                )

            # Step 3.1: Implement single-session security policy
            logger.info("Step 3.1: Implementing single-session security policy")
            try:
                from authentication.services.single_session_service import SingleSessionService

                session_result = SingleSessionService.implement_single_session_login(
                    user_id=member.id,
                    user_type='member',
                    tokens=tokens
                )

                if not session_result['success']:
                    logger.error(f"Single-session implementation failed: {session_result['error']}")
                    return service_error_response(
                        error_code=1005,  # Invalid token / Token generation failed
                        language=language
                    )

                logger.info(f"Single-session policy implemented successfully for member: {username}")

            except Exception as e:
                logger.error(f"Single-session implementation error: {str(e)}")
                return service_error_response(
                    error_code=1005,  # Invalid token / Token generation failed
                    language=language
                )
            
            # Step 4: ดึงข้อมูลโปรไฟล์
            logger.info("Step 4: Getting member profile")
            try:
                user_profile = MemberAuthService.get_member_profile(member)
            except Exception as e:
                logger.error(f"Profile retrieval failed: {str(e)}")
                return service_error_response(
                    error_code=3002,  # Data not found
                    language=language
                )
            
            # Step 5-9: Data processing steps
            processing_result = MemberAuthService._process_login_data(
                request, member, user_profile, tokens, username, language
            )
            
            if not processing_result['success']:
                return processing_result
            
            # บันทึก app logs เมื่อ login สำเร็จ
            ActionLogService.log_successful_login(request, member, 'member', language)
            
            # Step 10: เสร็จสิ้นการเข้าสู่ระบบ
            logger.info(f"authentication completed successfully for member: {username}")
            
            # Prepare final response data
            response_data = {
                'user': user_profile,
                'tokens': tokens,
                'session_info': {
                    'login_time': timezone.now().isoformat(),
                    'user_type': 'member'
                }
            }
            
            return service_success_response(
                data=response_data,
                language=language
            )
            
        except Exception as e:
            logger.error(f"Unexpected authentication error for member {username}: {str(e)}")
            return service_error_response(
                error_code=5000,  # System error occurred
                language=language
            )
    
    @staticmethod
    def _authenticate_credentials(request, username, password, language='th'):
        """
        ตรวจสอบการเข้าสู่ระบบของ Member (Internal method)
        
        Args:
            username: ชื่อผู้ใช้
            password: รหัสผ่าน
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        try:
            member = TcdAppMember.objects.get(username=username)
            
            # ตรวจสอบสถานะ member
            if member.status == MemberStatus.DELETED:
                ActionLogService.log_login_by_deleted_account(request, member, language)
                
                logger.warning(f"Member {username} has deleted status: {member.status}")
                return {
                    'success': False,
                    'error_code': 1001
                }
            elif not MemberStatus.is_active(member.status):
                # กรณีสถานะ app_member.status != 1 จะต้องแสดงข้อความ alert "ชื่อผู้ใช้งานโดนล็อค"
                logger.warning(f"Member {username} has inactive status: {member.status}")
                return {
                    'success': False,
                    'error_code': 1004  # Username is locked
                }
            
            # ตรวจสอบรหัสผ่าน
            if verify_password(password, member.password):
                # รีเซ็ต checklogin เมื่อ login สำเร็จ
                member.checklogin = 0
                member.lockout_end_date = None
                member.save()
                logger.info(f"Password verified successfully for {username}, reset checklogin to 0")
                
                return {
                    'success': True,
                    'member': member
                }
            else:
                # ไม่ต้องเพิ่ม checklogin ที่นี่ เพราะ serializer จัดการแล้ว
                # การเพิ่ม checklogin จะทำใน MemberLoginSerializer
                logger.warning(f"Password verification failed for {username}")
                return {
                    'success': False,
                    'error_code': 1001  # Invalid username or password
                }
                
        except TcdAppMember.DoesNotExist:
            logger.warning(f"Member not found: {username}")
            return {
                'success': False,
                'error_code': 1001  # Invalid username or password (don't reveal user doesn't exist)
            }
        except Exception as e:
            logger.error(f"Database error during credential authentication: {str(e)}")
            return {
                'success': False,
                'error_code': 3000  # Database error occurred
            }
    
    @staticmethod
    def _process_login_data(request, member, user_profile, tokens, username, language):
        """
        ประมวลผลข้อมูลการ login (Steps 5-9)
        
        Args:
            request: Django request object
            member: Member object
            user_profile: ข้อมูล profile
            tokens: JWT tokens
            username: ชื่อผู้ใช้
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        try:
            # Step 5: บันทึกข้อมูลการเข้าสู่ระบบ
            save_result = save_login_data(user_profile, 'member')
            if not save_result['success']:
                logger.error("Failed to save login data")
                return service_error_response(
                    error_code=3005,  # Cannot save data
                    language=language
                )
            
            # Step 6: ส่งข้อมูลผู้ใช้งาน
            send_result = send_user_data(user_profile, tokens)
            if not send_result['success']:
                logger.error("Failed to send user data")
                return service_error_response(
                    error_code=5000,  # Failed to connect to external service
                    language=language
                )
            
            # Step 7: ตรวจสอบการส่งข้อมูล
            transmission_result = validate_data_transmission(send_result['data'])
            if not transmission_result['success']:
                logger.error("Data transmission validation failed")
                return service_error_response(
                    error_code=5000,  # Invalid data from external service
                    language=language
                )
            
            # Step 8: แสดงข้อมูลผู้ใช้งาน
            display_result = prepare_user_display_data(user_profile, tokens)
            if not display_result['success']:
                logger.error("Failed to prepare user display data")
                return service_error_response(
                    error_code=5000,  # System error occurred
                    language=language
                )
            
            # Step 9: บันทึกเข้าสู่ระบบ (Login Record)
            client_ip = get_client_ip(request)
            record_result = save_login_record(
                user_id=member.id,
                username=username,
                user_type='member',
                ip_address=client_ip
            )
            if not record_result['success']:
                logger.error("Failed to save login record")
                return service_error_response(
                    error_code=3005,  # Cannot save data
                    language=language
                )
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error in login data processing: {str(e)}")
            return service_error_response(
                error_code=5000,  # System error occurred
                language=language
            )
    
    @staticmethod
    def generate_tokens(member):
        """
        สร้าง JWT tokens สำหรับ Member
        
        Args:
            member: Member object
            
        Returns:
            dict: JWT tokens
        """
        refresh = RefreshToken()
        
        # เพิ่มข้อมูล custom ใน token
        refresh['user_id'] = int(member.id) if member.id else None
        refresh['username'] = str(member.username) if member.username else None
        refresh['user_type'] = 'member'
        refresh['email'] = str(member.email) if member.email else None
        refresh['first_name'] = str(member.first_name) if member.first_name else None
        refresh['last_name'] = str(member.last_name) if member.last_name else None
        
        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }
    
    @staticmethod
    def get_member_profile(member):
        """
        ดึงข้อมูลโปรไฟล์ของ Member
        
        Args:
            member: Member object
            
        Returns:
            dict: Member profile data
        """
        # นำเข้า constants ที่จำเป็น
        from authentication.constants import MemberStatus
        
        # ดึงข้อมูลจาก master data tables สำหรับ display fields
        member_type = None
        government_sector = None
        ministry = None
        department = None
        
        # พยายามดึง member_type จาก database ถ้ามี
        if member.app_mas_member_type_id:
            try:
                member_type_obj = TcdAppMasMemberType.objects.filter(id=member.app_mas_member_type_id).first()
                if member_type_obj:
                    if member.lang == 'th':
                        member_type = member_type_obj.name_th
                    else:
                        member_type = member_type_obj.name_en
            except Exception as e:
                logger.error(f"Error getting member type display: {str(e)}")
                member_type = {'error': f"Member Type ID: {member.app_mas_member_type_id}"}

        # พยายามดึง government_sector จาก database ถ้ามี
        if member.app_mas_government_sector_id:
            try:
                government_sector = TcdAppMasGovernmentSector.objects.filter(id=member.app_mas_government_sector_id).first()
                if government_sector:
                    if member.lang == 'th':
                        government_sector = government_sector.name_th
                    else:
                        government_sector = government_sector.name_en
            except Exception as e:
                logger.error(f"Error getting government sector display: {str(e)}")
                government_sector = f"Government Sector ID: {member.app_mas_government_sector_id}"

        # พยายามดึง ministry จาก database ถ้ามี
        if member.app_mas_ministry_id:
            try:
                ministry = TcdAppMasMinistry.objects.filter(id=member.app_mas_ministry_id).first()
                if ministry:
                    if member.lang == 'th':
                        ministry = ministry.name_th
                    else:
                        ministry = ministry.name_en
            except Exception as e:
                logger.error(f"Error getting ministry display: {str(e)}")
                ministry = f"Ministry ID: {member.app_mas_ministry_id}"

        # พยายามดึง department จาก database ถ้ามี
        if member.app_mas_department_id:
            try:
                department = TcdAppMasDepartment.objects.filter(id=member.app_mas_department_id).first()
                if department:
                    if member.lang == 'th':
                        department = department.name_th
                    else:
                        department = department.name_en
            except Exception as e:
                logger.error(f"Error getting department display: {str(e)}")
                department = f"Department ID: {member.app_mas_department_id}"
        
        # Handle profile picture data (base64 or file path)
        # profile_picture_data = None
        profile_picture_type = None

        if member.src:
            if member.src.startswith('data:'):
                # Base64 encoded image
                # profile_picture_data = member.src
                profile_picture_type = 'base64'
                logger.info(f"Profile picture is base64 encoded, length: {len(member.src)}")
            else:
                # File path (legacy support)
                from django.conf import settings
                base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
                media_prefix = getattr(settings, 'MEDIA_PREFIX', '/media/')
                # profile_picture_data = f"{base_url}{media_prefix}{member.src}"
                profile_picture_type = 'url'
                logger.info(f"Profile picture is file path: {member.src}")

        # ดึงจำนวน app_project ที่เกี่ยวข้องกับ member นี้
        app_project_count = 0
        try:
            from project.models import TcdAppProject
            app_project_count = TcdAppProject.objects.filter(app_member=member.id).count()
            logger.info(f"Found {app_project_count} projects for member {member.username}")
            
        except ImportError:
            logger.warning("search.models module not found, app_project count will be 0")
            app_project_count = 0
        except Exception as e:
            logger.error(f"Error getting app_project count: {str(e)}")
            app_project_count = 0

        # สร้างข้อมูล profile พร้อมกับ display fields และจำนวน app_project
        return {
            'id': int(member.id) if member.id else None,
            'name': str(member.name) if member.name else None,
            'first_name': str(member.first_name) if member.first_name else None,
            'last_name': str(member.last_name) if member.last_name else None,
            'email': str(member.email) if member.email else None,
            'phone': str(member.phone) if member.phone else None,
            'identity_card_no': str(member.identity_card_no) if member.identity_card_no else None,
            'src': str(member.src) if member.src else None,
            # 'profile_picture_data': profile_picture_data,
            # 'profile_picture_type': profile_picture_type,
            'username': str(member.username) if member.username else None,
            'app_mas_member_type_id': int(member.app_mas_member_type_id) if member.app_mas_member_type_id else None,
            'app_mas_member_type': member_type,
            'app_mas_government_sector_id': int(member.app_mas_government_sector_id) if member.app_mas_government_sector_id else None,
            'app_mas_government_sector': government_sector,
            'app_mas_government_sector_other': member.app_mas_government_sector_other,
            'app_mas_ministry_id': int(member.app_mas_ministry_id) if member.app_mas_ministry_id else None,
            'app_mas_ministry': ministry,
            'app_mas_ministry_other': member.app_mas_ministry_other,
            'app_mas_department_id': int(member.app_mas_department_id) if member.app_mas_department_id else None,
            'app_mas_department': department,
            'app_mas_department_other': member.app_mas_department_other,
            'website': str(member.website) if member.website else None,
            'create_date': member.create_date.isoformat() if member.create_date else None,
            'is_notification': str(member.is_notification) if member.is_notification else None,
            'status': str(member.status) if member.status else None,
            'status_display': dict(MemberStatus.CHOICES).get(member.status, 'ไม่ระบุ'),
            'lang': str(member.lang) if member.lang else None,
            'user_type': 'member',
            'app_project_count': app_project_count
        }

    @staticmethod
    def register_member(username, email, password, first_name, last_name, phone, identity_card_no, language='th'):
        """
        สมัครสมาชิก Member ใหม่
        
        Args:
            username (str): ชื่อผู้ใช้
            email (str): อีเมล
            password (str): รหัสผ่าน
            first_name (str): ชื่อจริง
            last_name (str): นามสกุล
            phone (str): เบอร์โทรศัพท์
            identity_card_no (str): หมายเลขบัตรประชาชน
            language (str): ภาษา ('th' หรือ 'en')
            
        Returns:
            dict: Standard response format
        """
        from django.db import transaction
        
        logger.info(f"Starting member registration for: {username}")
        
        try:
            # ตรวจสอบข้อมูลซ้ำ
            logger.info("Checking for duplicate data")
            
            # ตรวจสอบ username ซ้ำ
            if TcdAppMember.objects.filter(username=username).exists():
                logger.warning(f"Username already exists: {username}")
                return service_error_response(
                    error_code=2010,  # Username already exists
                    language=language
                )
            
            # ตรวจสอบ email ซ้ำ (รวมทั้ง backup email)
            email_check_result = MemberAuthService.check_email_duplicate(email, language)
            if not email_check_result.get('success', False):
                logger.warning(f"Email duplicate check failed: {email}")
                return email_check_result

            # ตรวจสอบ phone ซ้ำ (รวมทั้ง backup phone)
            phone_check_result = MemberAuthService.check_phone_duplicate(phone, language)
            if not phone_check_result.get('success', False):
                logger.warning(f"Phone duplicate check failed: {phone}")
                return phone_check_result
            
            # ตรวจสอบ identity_card_no ซ้ำ
            if identity_card_no and TcdAppMember.objects.filter(identity_card_no=identity_card_no).exists():
                logger.warning(f"Identity card already exists: {identity_card_no}")
                return service_error_response(
                    error_code=2013,  # Identity card already exists
                    language=language
                )
            
            # สร้าง Member ใหม่
            logger.info("Creating new member")
            with transaction.atomic():
                # Hash รหัสผ่าน
                hashed_password = md5_hash(password)
                
                # สร้าง Member object
                member = TcdAppMember.objects.create(
                    username=username,
                    email=email,
                    password=hashed_password,
                    first_name=first_name,
                    last_name=last_name,
                    phone=phone,
                    identity_card_no=identity_card_no,
                    status=MemberStatus.ACTIVE,  # สถานะเริ่มต้นเป็น Active
                    lang=language,
                    create_date=timezone.now(),
                    checklogin=0,
                    is_notification='Y'
                )
                
                logger.info(f"Member created successfully with ID: {member.id}")
                
                # สร้าง JWT tokens
                logger.info("Generating JWT tokens for new member")
                tokens = MemberAuthService.generate_tokens(member)
                
                # ดึงข้อมูล profile
                logger.info("Getting member profile")
                user_profile = MemberAuthService.get_member_profile(member)
                
                # เตรียมข้อมูลสำหรับ response
                response_data = {
                    'user': user_profile,
                    'tokens': tokens,
                    'message': 'สมัครสมาชิกสำเร็จ' if language == 'th' else 'Registration successful'
                }
                
                logger.info(f"Member registration completed successfully: {username}")
                
                return service_success_response(
                    data=response_data,
                    language=language
                )
                
        except Exception as e:
            logger.error(f"Error during member registration: {str(e)}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )

    @staticmethod
    def register_member_full(validated_data, request=None, language='th'):
        """
        สมัครสมาชิก Member ใหม่ด้วยข้อมูลครบถ้วนจาก MemberRegistrationSerializer
        
        Args:
            validated_data (dict): ข้อมูลที่ผ่านการ validate แล้วจาก serializer
            request: Django request object (สำหรับ logging)
            language (str): ภาษา ('th' หรือ 'en')
            
        Returns:
            dict: Standard response format
        """
        from django.db import transaction
        
        username = validated_data.get('username')
        logger.info(f"Starting full member registration for: {username}")
        
        try:
            # สร้าง Member ใหม่
            logger.info("Creating new member with full data")
            with transaction.atomic():
                # Hash รหัสผ่าน
                password = validated_data.get('password')
                hashed_password = md5_hash(password)
                
                # เตรียมข้อมูลสำหรับสร้าง Member
                member_data = {
                    'username': validated_data.get('username'),
                    'email': validated_data.get('email'),
                    'password': hashed_password,
                    'first_name': validated_data.get('first_name'),
                    'last_name': validated_data.get('last_name'),
                    'status': MemberStatus.ACTIVE,  # สถานะเริ่มต้นเป็น Active
                    'lang': validated_data.get('lang', language),
                    'create_date': timezone.now(),
                    'checklogin': 0,
                    'is_notification': validated_data.get('is_notification', 'Y')
                }
                
                # เพิ่มฟิลด์ optional ที่มีค่า
                optional_fields = [
                    'name', 'phone', 'identity_card_no', 'website',
                    'fb_id', 'google_id', 'apple_id',
                    'app_mas_government_sector_other', 'app_mas_ministry_other', 
                    'app_mas_department_other'
                ]
                
                for field in optional_fields:
                    value = validated_data.get(field)
                    if value:  # เฉพาะค่าที่ไม่เป็น None หรือ empty string
                        member_data[field] = value
                
                # เพิ่มฟิลด์ foreign key ที่มีค่า
                fk_fields = {
                    'app_mas_member_type_id': 'app_mas_member_type_id',
                    'app_mas_government_sector_id': 'app_mas_government_sector_id',
                    'app_mas_ministry_id': 'app_mas_ministry_id',
                    'app_mas_department_id': 'app_mas_department_id'
                }
                
                for field, db_field in fk_fields.items():
                    value = validated_data.get(field)
                    if value is not None:
                        member_data[db_field] = value
                
                # สร้าง Member object
                member = TcdAppMember.objects.create(**member_data)
                
                logger.info(f"Member created successfully with ID: {member.id}")
                
                # สร้าง JWT tokens
                logger.info("Generating JWT tokens for new member")
                tokens = MemberAuthService.generate_tokens(member)

                # Implement single-session security policy for registration
                logger.info("Implementing single-session security policy for new member registration")
                try:
                    from authentication.services.single_session_service import SingleSessionService

                    session_result = SingleSessionService.implement_single_session_login(
                        user_id=member.id,
                        user_type='member',
                        tokens=tokens
                    )

                    if not session_result['success']:
                        logger.error(f"Single-session implementation failed during registration: {session_result['error']}")
                        return service_error_response(
                            error_code=1005,  # Invalid token / Token generation failed
                            language=language
                        )

                    logger.info(f"Single-session policy implemented successfully for new member: {username}")

                except Exception as e:
                    logger.error(f"Single-session implementation error during registration: {str(e)}")
                    return service_error_response(
                        error_code=1005,  # Invalid token / Token generation failed
                        language=language
                    )

                # ดึงข้อมูล profile
                logger.info("Getting member profile")
                user_profile = MemberAuthService.get_member_profile(member)
                
                # บันทึก registration log
                AppLogsService.log_registration(request, member, language)
                
                # เตรียมข้อมูลสำหรับ response
                response_data = {
                    'user': user_profile,
                    'tokens': tokens,
                    'message': 'สมัครสมาชิกสำเร็จ' if language == 'th' else 'Registration successful'
                }
                
                logger.info(f"Member registration completed successfully: {username}")
                
                return service_success_response(
                    data=response_data,
                    language=language
                )
                
        except Exception as e:
            logger.error(f"Error during full member registration: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )

    @staticmethod
    def upload_profile_picture_base64(member_id, base64_string, request=None, language='th'):
        """
        อัปโหลดรูปโปรไฟล์สำหรับ Member ด้วย base64 string (แบบง่าย ไม่มี validation)

        Args:
            member_id (int): ID ของ Member
            base64_string (str): Base64 string ของรูปภาพ
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: Standard response format
        """
        from django.db import transaction

        logger.info(f"Starting profile picture upload for member: {member_id}")

        try:
            # ตรวจสอบว่า member มีอยู่จริง
            try:
                member = TcdAppMember.objects.get(id=member_id)
            except TcdAppMember.DoesNotExist:
                logger.error(f"Member not found: {member_id}")
                return service_error_response(
                    error_code=1000,  # User not found
                    language=language
                )

            # ตรวจสอบว่ามี base64_string หรือไม่
            if not base64_string:
                logger.error("Base64 string is required")
                return service_error_response(
                    error_code=2001,  # Required field missing
                    language=language
                )

            # บันทึกข้อมูลลง database โดยตรง
            with transaction.atomic():
                member.src = base64_string
                member.save(update_fields=['src'])
                
                logger.info(f"Profile picture saved successfully for member: {member_id}")
                
            try:
                tcd_app_log = TcdAppLogs(
                    action_date=timezone.now(),
                    action_log="เปลี่ยนรูปภาพ",
                    remark="สำเร็จ",
                    ip_address=get_client_ip(request) or "",
                    app_member_id=member.id,
                    name=f"{member.first_name} {member.last_name}",
                    type="APPMEMBER"
                )
                tcd_app_log.save()
            except Exception as e:
                logger.error(f"Error logging app logs for profile picture upload: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")

            return {
                'status': True,
                'error_message': None,
                'error_code': None,
                'data': {
                    'src': base64_string,
                    'message': 'Profile picture uploaded successfully' if language == 'en' else 'อัปโหลดรูปโปรไฟล์สำเร็จ'
                }
            }

        except Exception as e:
            logger.error(f"Unexpected error in upload_profile_picture_base64: {e}")
            return service_error_response(
                error_code=9999,  # System error
                language=language
            )

    @staticmethod
    def upload_profile_picture(member_id, uploaded_file, language='th'):
        """
        อัปโหลดรูปโปรไฟล์สำหรับ Member และเข้ารหัสเป็น base64 (Legacy method)

        Args:
            member_id (int): ID ของ Member
            uploaded_file: Django UploadedFile object
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: Standard response format
        """
        import os
        import base64
        from django.db import transaction

        logger.info(f"Starting profile picture upload for member: {member_id}")

        try:
            # ตรวจสอบว่า member มีอยู่จริง
            try:
                member = TcdAppMember.objects.get(id=member_id)
            except TcdAppMember.DoesNotExist:
                logger.error(f"Member not found: {member_id}")
                return service_error_response(
                    error_code=1000,  # User not found
                    language=language
                )

            # ตรวจสอบประเภทไฟล์
            if not uploaded_file.content_type.startswith('image/'):
                logger.error(f"Invalid file type: {uploaded_file.content_type}")
                return service_error_response(
                    error_code=2006,  # Invalid file type
                    language=language
                )

            # ตรวจสอบขนาดไฟล์ (5MB)
            max_size = 5 * 1024 * 1024  # 5MB
            if uploaded_file.size > max_size:
                logger.error(f"File too large: {uploaded_file.size} bytes")
                return service_error_response(
                    error_code=2005,  # File too large
                    language=language
                )

            # ตรวจสอบนามสกุลไฟล์
            file_extension = os.path.splitext(uploaded_file.name)[1].lower()
            if file_extension not in ['.jpg', '.jpeg', '.png', '.gif']:
                logger.error(f"Unsupported file extension: {file_extension}")
                return service_error_response(
                    error_code=2006,  # Invalid file type
                    language=language
                )

            with transaction.atomic():
                # อ่านไฟล์และเข้ารหัสเป็น base64
                uploaded_file.seek(0)  # Reset file pointer
                file_content = uploaded_file.read()

                # เข้ารหัสเป็น base64
                base64_encoded = base64.b64encode(file_content).decode('utf-8')

                # สร้าง data URL format
                mime_type = uploaded_file.content_type
                data_url = f"data:{mime_type};base64,{base64_encoded}"

                # อัปเดต database
                member.src = data_url
                member.save(update_fields=['src'])

                logger.info(f"Profile picture uploaded and encoded successfully for member: {member_id}")
                logger.info(f"Base64 data length: {len(base64_encoded)} characters")

                # เตรียมข้อมูลสำหรับ response
                response_data = {
                    'src': data_url,
                    'base64_data': base64_encoded,
                    'mime_type': mime_type,
                    'file_size': uploaded_file.size,
                    'message': 'อัปโหลดรูปโปรไฟล์สำเร็จ' if language == 'th' else 'Profile picture uploaded successfully'
                }

                return service_success_response(
                    data=response_data,
                    language=language
                )

        except Exception as e:
            logger.error(f"Error uploading profile picture: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )


class ConsultantAuthService:
    """
    Service สำหรับ authentication ของ Consultant ที่ใช้ Standard Response Format
    """
    
    @staticmethod
    def authenticate(request, username, password):
        """
        ตรวจสอบการเข้าสู่ระบบของ Consultant ตาม Flowchart
        
        Args:
            request: Django request object
            username: ชื่อผู้ใช้
            password: รหัสผ่าน
            
        Returns:
            dict: Standard response format
        """
        # Get language from request
        language = get_language_from_request(request)
        
        logger.info(f"Starting authentication for consultant: {username}")
        
        try:
            # Step 1: API Connection validation
            api_validation = ConsultantAuthService._validate_api_prerequisites(request, language)
            if not api_validation['success']:
                return api_validation
            
            # Step 2: ตรวจสอบการเข้าสู่ระบบ
            logger.info("Step 2: Authenticating user credentials")
            auth_result = ConsultantAuthService._authenticate_credentials(username, password, language)
            if not auth_result['success']:
                logger.error(f"Consultant authentication failed: {auth_result.get('error_code')}")
                
                # บันทึก action log เมื่อเกิด error_code 1001
                if auth_result.get('error_code') == 1001:
                    ActionLogService.log_failed_login_attempt(request, username, auth_result.get('error_code'), language)
                
                if auth_result.get('error_code') == 1007:
                    user_consult = TcdUserConsult.objects.get(username=username)
                    TcdAppLogs.objects.create(
                        action_date=timezone.now(),
                        action_log='พยายามลงชื่อเข้าใช้งาน',
                        remark='รหัสผ่านไม่ถูกต้อง',
                        ip_address=get_client_ip(request) or '',
                        user_consult_id=user_consult.id,
                        app_member_id=None,
                        name=user_consult.username,
                        type='APPCONSULTANT'
                    )
                
                return service_error_response(
                    error_code=auth_result['error_code'],
                    language=language
                )
            
            consultant = auth_result['consultant']
            logger.info(f"Consultant authentication successful: {username}")
            
            # ตรวจสอบสถานะยื่นเรื่องขึ้นทะเบียนของที่ปรึกษา
            worklist = TcdWorklist.objects.filter(
                user_consult_id=consultant.id,
                request_type_id=1
            )
            
            is_not_registered = False
            if worklist:
                for item in worklist:
                    if item.status_work == '8':
                        is_not_registered = True
                        break
            else:
                is_not_registered = True
            
            if is_not_registered:
                logger.error(f"Consultant registration is not approved: {username}")
                return service_error_response(
                    error_code=1008, # Consultant is not registered
                    language=language
                )

            processing_result = ConsultantAuthService._process_authentication_data(
                request, consultant, username, language
            )
            
            if not processing_result['success']:
                return processing_result
            
            # เสร็จสิ้นการเข้าสู่ระบบ
            logger.info(f"authentication completed successfully for consultant: {username}")
            
            return service_success_response(
                data=processing_result['data'],
                language=language
            )
            
        except Exception as e:
            logger.error(f"Unexpected authentication error for consultant {username}: {str(e)}")
            return service_error_response(
                error_code=5000,  # System error occurred
                language=language
            )
    
    @staticmethod
    def _validate_api_prerequisites(request, language):
        """
        ตรวจสอบ API connection
        
        Args:
            request: Django request object
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        # Step 1: ตรวจสอบการเชื่อมต่อ API
        logger.info("Step 1: Validating API connection")
        api_connection = validate_api_connection()
        if not api_connection['success']:
            logger.error("API connection failed")
            return service_error_response(
                error_code=5000,  # Server unavailable
                language=language
            )
        
        return {'success': True}
    
    @staticmethod
    def _authenticate_credentials(username, password, language='th'):
        """
        ตรวจสอบการเข้าสู่ระบบของ Consultant (Internal method)
        
        Args:
            username: ชื่อผู้ใช้
            password: รหัสผ่าน
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        try:
            consultant = TcdUserConsult.objects.get(username=username)
            if not consultant:
                return {
                    'success': False,
                    'error_code': 1001  # Invalid username
                }
            
            # ตรวจสอบการยืนยันตัวตน
            if consultant.verify != ConsultantVerificationStatus.VERIFIED:
                return {
                    'success': False,
                    'error_code': 1003  # User account is not verified
                }
            
            # ตรวจสอบรหัสผ่าน
            if verify_password(password, consultant.password):
                return {
                    'success': True,
                    'consultant': consultant
                }
            else:
                return {
                    'success': False,
                    'error_code': 1007  # Invalid password
                }
                
        except TcdUserConsult.DoesNotExist:
            return {
                'success': False,
                'error_code': 1001  # Invalid username or password
            }
        except Exception as e:
            logger.error(f"Database error during consultant credential authentication: {str(e)}")
            return {
                'success': False,
                'error_code': 3000  # Database error occurred
            }
    
    @staticmethod
    def _process_authentication_data(request, consultant, username, language):
        """
        ประมวลผลข้อมูลการ authentication สำหรับ consultant
        
        Args:
            request: Django request object
            consultant: Consultant object
            username: ชื่อผู้ใช้
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        try:
            # Generate tokens
            tokens = ConsultantAuthService.generate_tokens(consultant)

            # Implement single-session security policy
            logger.info("Implementing single-session security policy for consultant")
            try:
                from authentication.services.single_session_service import SingleSessionService

                session_result = SingleSessionService.implement_single_session_login(
                    user_id=consultant.id,
                    user_type='consultant',
                    tokens=tokens
                )

                if not session_result['success']:
                    logger.error(f"Single-session implementation failed: {session_result['error']}")
                    return service_error_response(
                        error_code=1005,  # Invalid token / Token generation failed
                        language=language
                    )

                logger.info(f"Single-session policy implemented successfully for consultant: {username}")

            except Exception as e:
                logger.error(f"Single-session implementation error: {str(e)}")
                return service_error_response(
                    error_code=1005,  # Invalid token / Token generation failed
                    language=language
                )

            # Get profile
            user_profile = ConsultantAuthService.get_consultant_profile(consultant)
            
            # Process login data (simplified for consultant)
            client_ip = get_client_ip(request)
            record_result = save_login_record(
                user_id=consultant.id,
                username=username,
                user_type='consultant',
                ip_address=client_ip
            )
            
            if not record_result['success']:
                return service_error_response(
                    error_code=3005,  # Cannot save data
                    language=language
                )
            
            # บันทึก app logs เมื่อ login สำเร็จ
            ActionLogService.log_successful_login(request, consultant, 'consultant', language)
            
            # อัปเดต is_active_matching = 1 เมื่อ login สำเร็จ
            try:
                consultant.is_active_matching = 1
                consultant.save(update_fields=['is_active_matching'])
                logger.info(f"Updated is_active_matching to 1 for consultant: {username}")
            except Exception as e:
                logger.error(f"Failed to update is_active_matching for consultant {username}: {str(e)}")
                # ไม่ return error เพราะ login สำเร็จแล้ว
            
            # Prepare response data
            response_data = {
                'user': user_profile,
                'tokens': tokens,
                'session_info': {
                    'login_time': timezone.now().isoformat(),
                    'user_type': 'consultant'
                }
            }
            
            return {
                'success': True,
                'data': response_data
            }
            
        except Exception as e:
            logger.error(f"Error processing consultant authentication data: {str(e)}")
            return service_error_response(
                error_code=5000,  # System error occurred
                language=language
            )
    
    @staticmethod
    def generate_tokens(consultant):
        """
        สร้าง JWT tokens สำหรับ Consultant

        Args:
            consultant: Consultant object

        Returns:
            dict: JWT tokens
        """
        refresh = RefreshToken()

        # Get consultant team info for first_name and last_name
        consultant_team = None
        try:
            consultant_team = TcdUserConsultTeam.objects.filter(
                user_consult=consultant,
                is_admin=True
            ).first()
        except Exception:
            pass

        # เพิ่มข้อมูล custom ใน token
        refresh['user_id'] = int(consultant.id) if consultant.id else None
        refresh['username'] = str(consultant.username) if consultant.username else None
        refresh['user_type'] = 'consultant'
        refresh['email'] = str(consultant.email) if consultant.email else None

        # Add first_name and last_name from consultant team if available
        if consultant_team:
            refresh['first_name'] = str(consultant_team.first_name) if consultant_team.first_name else None
            refresh['last_name'] = str(consultant_team.last_name) if consultant_team.last_name else None
        else:
            refresh['first_name'] = None
            refresh['last_name'] = None

        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }
    
    @staticmethod
    def get_consultant_profile(consultant):
        """
        ดึงข้อมูลโปรไฟล์ของ Consultant
        
        Args:
            consultant: Consultant object
            
        Returns:
            dict: Consultant profile data
        """
        # นำเข้า constants ที่จำเป็น
        from authentication.constants import ConsultantVerificationStatus, ConsultantType
        
        # ดึงข้อมูลจาก master data tables สำหรับ display fields
        corporate_type_display = None
        
        # พยายามดึง corporate_type จาก database ถ้ามี
        if consultant.corporate_type_id:
            try:
                corporate_type = TcdCorporateType.objects.filter(id=consultant.corporate_type_id).first()
                if corporate_type:
                    corporate_type_display = corporate_type.name_th
            except Exception as e:
                logger.error(f"Error getting corporate type display: {str(e)}")
                corporate_type_display = f"Corporate Type ID: {consultant.corporate_type_id}"
        
        # สร้างข้อมูล profile พร้อมกับ display fields
        return {
            'id': int(consultant.id) if consultant.id else None,
            'consult_type': int(consultant.consult_type) if consultant.consult_type else None,
            'consult_type_display': dict(ConsultantType.CHOICES).get(int(consultant.consult_type) if consultant.consult_type else None, 'ไม่ระบุ'),
            'corporate_type_id': int(consultant.corporate_type_id) if consultant.corporate_type_id else None,
            'corporate_type_display': corporate_type_display or 'ไม่ระบุ',
            'username': str(consultant.username) if consultant.username else None,
            'email': str(consultant.email) if consultant.email else None,
            'email_second': str(consultant.email_second) if consultant.email_second else None,
            'phone': str(consultant.phone) if consultant.phone else None,
            'phone_second': str(consultant.phone_second) if consultant.phone_second else None,
            'maker_name': str(consultant.maker_name) if consultant.maker_name else None,
            'maker_phone': str(consultant.maker_phone) if consultant.maker_phone else None,
            'maker_email': str(consultant.maker_email) if consultant.maker_email else None,
            'verify': str(consultant.verify) if consultant.verify else None,
            'verify_display': dict(ConsultantVerificationStatus.CHOICES).get(consultant.verify, 'ไม่ระบุ'),
            'is_notification': str(consultant.is_notification) if consultant.is_notification else None,
            'score': float(consultant.score) if consultant.score else None,
            'lang': str(consultant.lang) if consultant.lang else None,
            'is_active_matching': bool(consultant.is_active_matching) if consultant.is_active_matching is not None else None,
            'src': str(consultant.src) if consultant.src else None,
            'user_type': 'consultant'
        }


class ActionLogService:
    """
    Service สำหรับจัดการ TcdActionLog
    
    ใช้สำหรับบันทึก action log ต่างๆ ในระบบ เช่น การ login ที่ไม่สำเร็จ
    
    ตัวอย่างการใช้งาน:
    
    # บันทึก failed login attempt (จะถูกเรียกอัตโนมัติใน authentication process)
    result = ActionLogService.log_failed_login_attempt(request, "testuser", 1001, "th")
    
    # บันทึก custom action log
    result = ActionLogService.log_custom_action(
        request=request,
        action_log_text="ผู้ใช้ทำการอัปเดตข้อมูลโปรไฟล์",
        remark="อัปเดตข้อมูลส่วนตัวสำเร็จ",
        user_name="john_doe",
        consult_name="",
        language="th"
    )
    
    # บันทึกการลงทะเบียน
    result = AppLogsService.log_registration(request, member, "th")
    
    ข้อมูลที่จะถูกบันทึกใน TcdActionLog:
    - action_date: วันที่และเวลาปัจจุบัน
    - action_log: ข้อความอธิบาย action
    - remark: หมายเหตุเพิ่มเติม
    - user_name: ชื่อผู้ใช้ (สำหรับ error_code 1001 จะเป็น "System error")
    - consult_name: ชื่อ consultant (สำหรับ error_code 1001 จะเป็น "")
    - ip_address: IP address ของ client
    """
    
    @staticmethod
    def log_failed_login_attempt(request, username, error_code, language='th'):
        """
        บันทึก TcdActionLog เมื่อเกิดข้อผิดพลาดในการ login
        
        Args:
            request: Django request object
            username: ชื่อผู้ใช้ที่พยายาม login
            error_code: รหัสข้อผิดพลาด
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        logger.info(f"ActionLogService.log_failed_login_attempt called with username: {username}, error_code: {error_code}")
        
        try:
            # ตรวจสอบว่าเป็น error_code 1001 (Invalid username or password)
            if error_code == 1001 or error_code == 1007:
                logger.info("Error code is 1001, proceeding to log action")
                
                # ดึง IP address จาก request
                client_ip = get_client_ip(request)
                logger.info(f"Client IP: {client_ip}")
                
                if error_code == 1001:
                    remark = f"โดยใช้ชื่อผู้ใช้งาน {username} แต่ไม่สำเร็จ"
                else:
                    remark = "รหัสผ่านไม่ถูกต้อง"
                
                # สร้าง action log record
                action_log = TcdActionLog(
                    action_date=timezone.now(),
                    action_log="พยายามลงชื่อเข้าใช้งาน",
                    remark=remark,
                    user_name=username,
                    consult_name="",
                    ip_address=client_ip or ""
                )
                
                logger.info(f"Created action log object: {action_log.__dict__}")
                
                # บันทึกลงฐานข้อมูล
                action_log.save()
                
                logger.info(f"Failed login attempt logged successfully for username: {username}, IP: {client_ip}")
                
                return service_success_response(
                    data={
                        'logged': True,
                        'message': 'บันทึก action log สำเร็จ'
                    },
                    language=language
                )
            else:
                logger.info(f"Error code {error_code} is not 1001, skipping action log")
                # ไม่ใช่ error_code 1001 ไม่ต้องบันทึก
                return service_success_response(
                    data={
                        'logged': False,
                        'message': 'ไม่ต้องบันทึก action log สำหรับ error code นี้'
                    },
                    language=language
                )
                
        except Exception as e:
            logger.error(f"Error logging failed login attempt: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3005,  # Cannot save data
                language=language
            )
    
    @staticmethod
    def log_custom_action(request, action_log_text, remark, user_name, consult_name="", language='th'):
        """
        บันทึก TcdActionLog แบบกำหนดเอง
        
        Args:
            request: Django request object
            action_log_text: ข้อความ action log
            remark: หมายเหตุ
            user_name: ชื่อผู้ใช้ (default: "System")
            consult_name: ชื่อ consultant (default: "")
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        try:
            # ดึง IP address จาก request
            client_ip = get_client_ip(request)
            
            # สร้าง action log record
            action_log = TcdActionLog(
                action_date=timezone.now(),
                action_log=action_log_text,
                remark=remark,
                user_name=user_name,
                consult_name=consult_name,
                ip_address=client_ip or ""
            )
            
            # บันทึกลงฐานข้อมูล
            action_log.save()
            
            logger.info(f"Custom action log saved: {action_log_text}")
            
            return service_success_response(
                data={
                    'logged': True,
                    'message': 'บันทึก action log สำเร็จ'
                },
                language=language
            )
            
        except Exception as e:
            logger.error(f"Error logging custom action: {str(e)}")
            return service_error_response(
                error_code=3005,  # Cannot save data
                language=language
            )
    
    @staticmethod
    def log_account_locked_attempt(request, member, error_code, language='th'):
        """
        บันทึก TcdAppLogs เมื่อเกิด error_code 1002 (Account locked)
        
        Args:
            request: Django request object
            member: TcdAppMember object
            error_code: รหัสข้อผิดพลาด
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        logger.info(f"ActionLogService.log_account_locked_attempt called with member_id: {member.id if member else 'None'}, error_code: {error_code}")
        
        try:
            # ตรวจสอบว่าเป็น error_code 1002 (Account locked)
            if error_code == 1002:
                logger.info("Error code is 1002, proceeding to log account locked attempt")
                
                # Import model ที่จำเป็น
                from authentication.models import TcdAppLogs
                
                # ดึง IP address จาก request
                client_ip = get_client_ip(request)
                logger.info(f"Client IP: {client_ip}")
                
                # สร้าง member name
                member_name = ""
                if member:
                    first_name = getattr(member, 'first_name', '') or ''
                    last_name = getattr(member, 'last_name', '') or ''
                    member_name = f"{first_name} {last_name}".strip()
                
                logger.info(f"Member name: {member_name}")
                
                # สร้าง app logs record
                app_logs = TcdAppLogs(
                    action_date=timezone.now(),
                    action_log="พยายามลงชื่อเข้าใช้งาน",
                    remark="รหัสผ่านผิดเกิน 3 ครั้ง",
                    ip_address=client_ip or "",
                    app_member_id=member.id if member else None,
                    name=member_name,
                    type="APPMEMBER",
                    user_consult_id=None  # สำหรับ member จะเป็น None
                )
                
                logger.info(f"Created app logs object: action_log={app_logs.action_log}, app_member_id={app_logs.app_member_id}, name={app_logs.name}")
                
                # บันทึกลงฐานข้อมูล
                app_logs.save()
                
                logger.info(f"Account locked attempt logged successfully for member_id: {member.id if member else 'None'}, IP: {client_ip}")
                
                return service_success_response(
                    data={
                        'logged': True,
                        'message': 'บันทึก account locked log สำเร็จ'
                    },
                    language=language
                )
            else:
                logger.info(f"Error code {error_code} is not 1002, skipping account locked log")
                # ไม่ใช่ error_code 1002 ไม่ต้องบันทึก
                return service_success_response(
                    data={
                        'logged': False,
                        'message': 'ไม่ต้องบันทึก account locked log สำหรับ error code นี้'
                    },
                    language=language
                )
                
        except Exception as e:
            logger.error(f"Error logging account locked attempt: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3005,  # Cannot save data
                language=language
            )
    
    @staticmethod
    def log_successful_login(request, user, user_type='member', language='th'):
        """
        บันทึก TcdAppLogs เมื่อ login สำเร็จ
        
        Args:
            request: Django request object
            user: TcdAppMember หรือ TcdUserConsult object
            user_type: ประเภทผู้ใช้ ('member' หรือ 'consultant')
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        logger.info(f"ActionLogService.log_successful_login called with user_id: {user.id if user else 'None'}, user_type: {user_type}")
        
        try:
            # ดึง IP address จาก request
            client_ip = get_client_ip(request)
            logger.info(f"Client IP: {client_ip}")
            
            # สร้าง user name
            user_name = ""
            if user_type == 'member':
                first_name = getattr(user, 'first_name', '') or ''
                last_name = getattr(user, 'last_name', '') or ''
                user_name = f"{first_name} {last_name}".strip()
            elif user_type == 'consultant':
                user_name = getattr(user, 'maker_name', '') or ''
            elif user_type == 'staff':
                first_name = getattr(user, 'firstname', '') or ''
                last_name = getattr(user, 'lastname', '') or ''
                user_name = f"{first_name} {last_name}".strip()
                if not user_name:
                    user_name = getattr(user, 'username', '') or ''
            
            logger.info(f"User name: {user_name}")
            
            # สร้าง app logs record
            app_logs = TcdAppLogs(
                action_date=timezone.now(),
                action_log="ลงชื่อเข้าใช้งาน",
                remark="สำเร็จ",
                ip_address=client_ip or "",
                app_member_id=user.id if user_type == 'member' else None,
                user_consult_id=user.id if user_type == 'consultant' else None,
                name=user_name,
                type="APPMEMBER" if user_type == 'member' else ("CONSULTANT" if user_type == 'consultant' else "STAFF")
            )
            
            logger.info(f"Created app logs object: action_log={app_logs.action_log}, app_member_id={app_logs.app_member_id}, user_consult_id={app_logs.user_consult_id}, name={app_logs.name}")
            
            # บันทึกลงฐานข้อมูล
            app_logs.save()
            
            logger.info(f"Successful login logged for user_id: {user.id}, type: {user_type}, IP: {client_ip}")
            
            return service_success_response(
                data={
                    'logged': True,
                    'message': 'บันทึก successful login log สำเร็จ'
                },
                language=language
            )
                
        except Exception as e:
            logger.error(f"Error logging successful login: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3005,  # Cannot save data
                language=language
            )

    @staticmethod
    def log_login_by_deleted_account(request, member, language='th'):
        """
        บันทึก TcdActionLog
        """
        try:
            # ดึง IP address จาก request
            client_ip = get_client_ip(request)
            
            tcd_action_log = TcdActionLog(
                action_date=timezone.now(),
                action_log="พยายามลงชื่อเข้าใช้งานแอปพลิเคชัน",
                remark=f"โดยใช้ชื่อผู้ใช้งาน {member.username} แต่ไม่สำเร็จ",
                user_name="System error",
                consult_name="",
                ip_address=client_ip or ""
            )
            tcd_action_log.save()
            
            return service_success_response(
                data={
                    'logged': True,
                    'message': 'บันทึก action log สำเร็จ'
                },
                language=language
            )
        except Exception as e:
            logger.error(f"Error logging action log: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3005,  # Cannot save data
                language=language
            )


class AppLogsService:
    @staticmethod
    def log_registration(request, member, language='th'):
        """
        บันทึก TcdActionLog เมื่อมีการลงทะเบียนสมาชิกใหม่
        
        Args:
            request: Django request object
            username: ชื่อผู้ใช้ที่ลงทะเบียน
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        logger.info(f"ActionLogService.log_registration called with username: {member.username}")
        
        try:
            # Import model ที่จำเป็น
            from authentication.models import TcdAppLogs
            
            # ดึง IP address จาก request
            client_ip = get_client_ip(request)
            logger.info(f"Client IP: {client_ip}")
            
            # สร้าง action log record การลงทะเบียน
            app_logs_registration = TcdAppLogs(
                action_date=timezone.now(),
                action_log="สมัครสมาชิก",
                remark="สำเร็จ",
                ip_address=client_ip or "",
                app_member_id=member.id,
                name=f"{member.first_name} {member.last_name}",
                type="APPMEMBER"
            )
            
            # สร้าง action log record การ login
            app_logs_login = TcdAppLogs(
                action_date=timezone.now(),
                action_log="ลงชื่อเข้าใช้งาน",
                remark="สำเร็จ",
                ip_address=client_ip or "",
                app_member_id=member.id,
                name=f"{member.first_name} {member.last_name}",
                type="APPMEMBER"
            )
            
            logger.info(f"Created registration log object: {app_logs_registration.__dict__}")
            logger.info(f"Created login log object: {app_logs_login.__dict__}")
            
            # บันทึกลงฐานข้อมูล
            app_logs_registration.save()
            app_logs_login.save()
            
            logger.info(f"Registration logged successfully for username: {member.username}, IP: {client_ip}")
            
            return service_success_response(
                data={
                    'logged': True,
                    'message': 'บันทึก action log การลงทะเบียนสำเร็จ'
                },
                language=language
            )
                
        except Exception as e:
            logger.error(f"Error logging registration: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3005,  # Cannot save data
                language=language
            )


class PasswordResetService:
    """
    Service สำหรับการรีเซ็ตรหัสผ่าน
    ใช้ OTP service ที่มีอยู่แล้วในการยืนยันตัวตน
    """

    @staticmethod
    def request_password_reset(email, language='th'):
        """
        ขอรีเซ็ตรหัสผ่านโดยส่ง OTP ไปยังอีเมล
        รองรับอีเมลสำรอง (email_second) สำหรับที่ปรึกษา

        Args:
            email (str): อีเมลของผู้ใช้ (อีเมลหลักหรืออีเมลสำรอง)
            language (str): ภาษา

        Returns:
            dict: Standard response format
        """
        logger.info(f"Password reset request for email: {email}")

        try:
            # ตรวจสอบว่าอีเมลมีอยู่ในระบบหรือไม่
            member_exists = TcdAppMember.objects.filter(email=email).exists()
            consultant_exists = TcdUserConsult.objects.filter(email=email).exists()
            # ตรวจสอบอีเมลสำรองของที่ปรึกษา
            consultant_backup_exists = TcdUserConsult.objects.filter(email_second=email).exists()

            if not member_exists and not consultant_exists and not consultant_backup_exists:
                logger.warning(f"Email not found in system: {email}")
                return service_error_response(
                    error_code=1000,  # User not found
                    language=language
                )

            # สร้าง OTP สำหรับการรีเซ็ตรหัสผ่าน
            from authentication.services.otp_service import OTPService
            from authentication.constants import OTPPurpose
            from authentication.utils import send_otp_email

            otp_result = OTPService.generate_otp(email, OTPPurpose.RESET_PASSWORD)

            # ส่ง OTP ทางอีเมล
            email_result = send_otp_email(
                email,
                otp_result['otp'],
                otp_result['ref_code'],
                OTPPurpose.RESET_PASSWORD,
                language
            )

            if not email_result['success']:
                logger.error(f"Failed to send password reset OTP email: {email_result['error']}")
                return service_error_response(
                    error_code=2027,  # OTP generation failed
                    language=language
                )

            logger.info(f"Password reset OTP sent successfully to: {email}")

            return service_success_response(
                data={
                    "token": otp_result['token'],
                    "ref_code": otp_result['ref_code'],
                    "expires_at": otp_result['expires_at'].isoformat(),
                    "message": "รหัส OTP สำหรับรีเซ็ตรหัสผ่านได้ถูกส่งไปยังอีเมลของคุณแล้ว" if language == 'th' else "Password reset OTP has been sent to your email"
                },
                language=language
            )

        except Exception as e:
            logger.error(f"Error in password reset request: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )

    @staticmethod
    def verify_password_reset_otp(email, otp_token, otp_code, ref_code, language='th'):
        """
        ตรวจสอบ OTP สำหรับการรีเซ็ตรหัสผ่าน

        Args:
            email (str): อีเมลของผู้ใช้
            otp_token (str): Token จากการ verify OTP
            otp_code (str): รหัส OTP
            ref_code (str): รหัสอ้างอิง
            language (str): ภาษา

        Returns:
            dict: Standard response format with verification result
        """
        logger.info(f"Verifying password reset OTP for email: {email}")

        try:
            # ตรวจสอบ OTP
            from authentication.services.otp_service import OTPService
            verification_result = OTPService.verify_otp(otp_token, otp_code, ref_code)

            if not verification_result['success']:
                logger.warning(f"OTP verification failed for password reset: {email}")
                return verification_result

            # ตรวจสอบว่า OTP เป็นสำหรับ reset_password
            otp_data = verification_result['data']
            if otp_data.get('purpose') != 'reset_password':
                logger.error(f"Invalid OTP purpose for password reset: {otp_data.get('purpose')}")
                return service_error_response(
                    error_code=2025,  # OTP token invalid
                    language=language
                )

            # ตรวจสอบว่าอีเมลตรงกับที่ใช้ในการขอ OTP
            if otp_data.get('identifier') != email:
                logger.error(f"Email mismatch in password reset: {email} vs {otp_data.get('identifier')}")
                return service_error_response(
                    error_code=2025,  # OTP token invalid
                    language=language
                )

            logger.info(f"OTP verification successful for password reset: {email}")
            return service_success_response(
                data={
                    "verified": True,
                    "otp_data": otp_data
                },
                language=language
            )

        except Exception as e:
            logger.error(f"Error in password reset OTP verification: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )

    @staticmethod
    def update_user_password(email, new_password, language='th'):
        """
        อัปเดตรหัสผ่านของผู้ใช้
        รองรับอีเมลสำรอง (email_second) สำหรับที่ปรึกษา

        Args:
            email (str): อีเมลของผู้ใช้ (อีเมลหลักหรืออีเมลสำรอง)
            new_password (str): รหัสผ่านใหม่
            language (str): ภาษา

        Returns:
            dict: Standard response format with user_type information
        """
        logger.info(f"Updating password for email: {email}")

        try:
            # อัปเดตรหัสผ่าน
            from django.db import transaction

            with transaction.atomic():
                # Hash รหัสผ่านใหม่
                hashed_password = md5_hash(new_password)

                # ตรวจสอบและอัปเดตใน TcdAppMember
                member_updated = TcdAppMember.objects.filter(email=email).update(password=hashed_password)

                # ตรวจสอบและอัปเดตใน TcdUserConsult (อีเมลหลัก)
                consultant_updated = TcdUserConsult.objects.filter(email=email).update(password=hashed_password)

                # ตรวจสอบและอัปเดตใน TcdUserConsult (อีเมลสำรอง)
                consultant_backup_updated = TcdUserConsult.objects.filter(email_second=email).update(password=hashed_password)

                if not member_updated and not consultant_updated and not consultant_backup_updated:
                    logger.error(f"No user found to update password for email: {email}")
                    return service_error_response(
                        error_code=1000,  # User not found
                        language=language
                    )

                user_type = 'member' if member_updated else 'consultant'
                logger.info(f"Password updated successfully for {user_type}: {email}")

            return service_success_response(
                data={
                    "updated": True,
                    "user_type": user_type
                },
                language=language
            )

        except Exception as e:
            logger.error(f"Error updating user password: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )


class ChangePasswordService:
    """
    Service สำหรับการเปลี่ยนรหัสผ่าน (ไม่ต้องผ่าน OTP)
    """

    @staticmethod
    def change_user_password(user_id, user_type, current_password, new_password, request=None, language='th'):
        """
        เปลี่ยนรหัสผ่านของผู้ใช้

        Args:
            user_id (int): ID ของผู้ใช้
            user_type (str): ประเภทผู้ใช้ ('member' หรือ 'consultant')
            current_password (str): รหัสผ่านปัจจุบัน
            new_password (str): รหัสผ่านใหม่
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: ผลลัพธ์การเปลี่ยนรหัสผ่าน
        """
        logger.info(f"Change password request for user_id: {user_id}, user_type: {user_type}")

        try:
            from django.db import transaction
            from authentication.utils import verify_password, md5_hash

            with transaction.atomic():
                user = None

                # ดึงข้อมูลผู้ใช้ตามประเภท
                if user_type == 'member':
                    try:
                        user = TcdAppMember.objects.get(id=user_id)
                        logger.info(f"Found member: {user.username}")
                    except TcdAppMember.DoesNotExist:
                        logger.error(f"Member not found with id: {user_id}")
                        return service_error_response(
                            error_code=1000,  # User not found
                            language=language
                        )

                elif user_type == 'consultant':
                    try:
                        user = TcdUserConsult.objects.get(id=user_id)
                        logger.info(f"Found consultant: {user.username}")
                    except TcdUserConsult.DoesNotExist:
                        logger.error(f"Consultant not found with id: {user_id}")
                        return service_error_response(
                            error_code=1000,  # User not found
                            language=language
                        )
                else:
                    logger.error(f"Invalid user_type: {user_type}")
                    return service_error_response(
                        error_code=2000,  # Invalid data provided
                        language=language
                    )

                # ตรวจสอบรหัสผ่านปัจจุบัน
                if not verify_password(current_password, user.password):
                    logger.error(f"Current password verification failed for user_id: {user_id}")
                    return service_error_response(
                        error_code=1007,  # Invalid credentials
                        language=language
                    )

                # Hash รหัสผ่านใหม่
                hashed_new_password = md5_hash(new_password)

                # อัปเดตรหัสผ่าน
                user.password = hashed_new_password
                user.save()

                logger.info(f"Password changed successfully for {user_type}: {user.username}")

                if user_type == 'member':
                    from authentication.models import TcdAppLogs
                    from django.utils import timezone

                    TcdAppLogs.objects.create(
                        action_date=timezone.now(),
                        action_log="เปลี่ยนรหัสผ่าน",
                        remark="สำเร็จ",
                        ip_address=get_client_ip(request) or "",
                        app_member_id=user.id,
                        name=f"{user.first_name} {user.last_name}",
                        type="APPMEMBER"
                    )

                return {
                    'success': True,
                    'data': {
                        'message': 'เปลี่ยนรหัสผ่านสำเร็จ' if language == 'th' else 'Password changed successfully',
                        'user_type': user_type
                    }
                }

        except Exception as e:
            logger.error(f"Unexpected error during password change: {str(e)}")
            return service_error_response(
                error_code=3000,  # Internal server error
                language=language
            )


class DeleteMemberService:
    """
    Service สำหรับการลบสมาชิก Member
    เช็คจาก token ว่าเป็นตัวเองเท่านั้นที่ลบได้
    """

    @staticmethod
    def delete_member(user_id, current_password, language='th', request=None):
        """
        ลบสมาชิก Member โดยเช็คจาก token ว่าเป็นตัวเองเท่านั้นที่ลบได้ พร้อมตรวจสอบรหัสผ่าน

        Args:
            user_id (int): ID ของ Member ที่ต้องการลบ (จาก JWT token)
            current_password (str): รหัสผ่านปัจจุบันสำหรับยืนยันตัวตน
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: Standard response format
        """
        logger.info(f"Starting member deletion for ID: {user_id}")

        try:
            # ตรวจสอบว่า Member มีอยู่จริงหรือไม่
            try:
                member = TcdAppMember.objects.get(id=user_id)
                member_full_name = f"{member.first_name} {member.last_name}"
                logger.info(f"Found member to delete: {member.username} (ID: {member.id})")
            except TcdAppMember.DoesNotExist:
                logger.error(f"Member not found for deletion: {user_id}")
                return service_error_response(
                    error_code=1000,  # User not found
                    language=language
                )

            # ตรวจสอบสถานะของ Member ก่อนลบ
            if member.status == MemberStatus.DELETED:
                logger.warning(f"Member already deleted: {member.username}")
                return service_error_response(
                    error_code=1000,  # Data has been deleted
                    language=language
                )

            # ตรวจสอบสถานะ member
            if not MemberStatus.is_active(member.status):
                logger.error(f"Member account is not active: {member.username}, status: {member.status}")
                error_code = 1002 if member.status == 'L' else 1003  # Account locked or inactive
                return service_error_response(
                    error_code=error_code,
                    language=language
                )

            # ตรวจสอบรหัสผ่านปัจจุบัน
            if not verify_password(current_password, member.password):
                logger.error(f"Current password verification failed for member deletion: {member.username}")
                return service_error_response(
                    error_code=1007,  # Invalid credentials
                    language=language
                )

            # ลบ Member โดยการเปลี่ยนสถานะเป็น DELETED
            from django.db import transaction
            from django.utils import timezone
            from authentication.models import TcdAppMemberDelete
            from project.models import TcdAppProjectConsult, TcdAppProject, TcdAppMatchingLog, TcdAppProjectSector, TcdAppProjectSkill, TcdAppProjectService
            import uuid

            with transaction.atomic():
                count_matching = TcdAppProjectConsult.objects.filter(app_member_id=member.id).count()
                if count_matching > 0:
                    # Clone member data to TcdAppMemberDelete
                    delete_record = TcdAppMemberDelete.objects.create(
                        app_member=member,
                        first_name=member.first_name,
                        last_name=member.last_name,
                        email=member.email,
                        phone=member.phone,
                        identity_card_no=member.identity_card_no,
                        delete_date=timezone.now()
                    )

                    # Update member's personal data to "xxxxx" and username to uuid4
                    member.first_name = "xxxxx"
                    member.last_name = "xxxxx"
                    member.email = "xxxxx"
                    member.phone = "xxxxx"
                    member.identity_card_no = "xxxxx"
                    member.username = str(uuid.uuid4())
                    member.status = MemberStatus.DELETED
                    member.save()

                else:
                    # ลบข้อมูลทุกตารางที่เกี่ยวข้องกับ member นี้ โดยใช้ ORM และอยู่ใน transaction.atomic()

                    # 1. ลบ app_logs ที่เกี่ยวข้องกับ member
                    TcdAppLogs.objects.filter(app_member_id=member.id).delete()

                    # 2. หา project_ids ที่เป็นของ member นี้
                    project_ids = list(TcdAppProject.objects.filter(app_member_id=member.id).values_list('id', flat=True))

                    if project_ids:
                        # 3. ลบ app_matching_log ที่เกี่ยวข้องกับ project_ids เหล่านี้
                        TcdAppMatchingLog.objects.filter(app_project_id__in=project_ids).delete()
                        # 4. ลบ app_project_sector ที่เกี่ยวข้องกับ project_ids เหล่านี้
                        TcdAppProjectSector.objects.filter(app_project_id__in=project_ids).delete()
                        # 5. ลบ app_project_skill ที่เกี่ยวข้องกับ project_ids เหล่านี้
                        TcdAppProjectSkill.objects.filter(app_project_id__in=project_ids).delete()
                        # 6. ลบ app_project_service ที่เกี่ยวข้องกับ project_ids เหล่านี้
                        TcdAppProjectService.objects.filter(app_project_id__in=project_ids).delete()
                        # 7. ลบ app_project ที่เป็นของ member นี้
                        TcdAppProject.objects.filter(id__in=project_ids).delete()

                    # 8. ลบ app_member (ต้องลบไม่ว่าจะมี projects หรือไม่)
                    TcdAppMember.objects.filter(id=member.id).delete()

                logger.info(f"Member deleted successfully: {member.username} (ID: {member.id})")

                # Blacklist all JWT tokens for this member
                try:
                    from django.db import connection
                    with connection.cursor() as cursor:
                        # Find all outstanding tokens for this member
                        cursor.execute("""
                            SELECT ot.id, ot.jti
                            FROM tcd_outstanding_tokens ot
                            WHERE ot.user_id = %s
                            AND NOT EXISTS (
                                SELECT 1 FROM tcd_blacklisted_tokens bt
                                WHERE bt.token_id = ot.id
                            )
                        """, [member.id])

                        outstanding_tokens = cursor.fetchall()

                        # Blacklist each token
                        for token_id, jti in outstanding_tokens:
                            cursor.execute("""
                                INSERT INTO tcd_blacklisted_tokens (token_id, blacklisted_at)
                                VALUES (%s, %s)
                            """, [token_id, timezone.now()])
                            logger.info(f"Blacklisted token {jti} for deleted member {member.username}")

                        if outstanding_tokens:
                            logger.info(f"Blacklisted {len(outstanding_tokens)} tokens for member {member.username}")
                        else:
                            logger.info(f"No outstanding tokens found for member {member.username}")

                except Exception as blacklist_error:
                    logger.error(f"Error blacklisting tokens for member {member.username}: {str(blacklist_error)}")
                    # Continue with deletion even if blacklisting fails

                # Log the deletion action
                TcdAppLogs.objects.create(
                    action_date=timezone.now(),
                    action_log="ลบข้อมูล",
                    remark="สำเร็จ",
                    ip_address=get_client_ip(request),
                    app_member_id=member.id,
                    name=member_full_name,
                    type="APPMEMBER"
                )

                # Prepare response data based on deletion type
                response_data = {
                    'message': 'ลบสมาชิกสำเร็จ' if language == 'th' else 'Member deleted successfully',
                    'member_id': member.id,
                    'username': member.username,
                    'deleted_at': timezone.now().isoformat()
                }

                # Add delete_record_id only if it exists (when member has matching projects)
                if count_matching > 0:
                    response_data['delete_record_id'] = delete_record.id

                return service_success_response(
                    data=response_data,
                    language=language
                )

        except Exception as e:
            logger.error(f"Error during member deletion: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )


class UpdateMemberInfoService:
    """
    Service สำหรับการอัปเดตข้อมูลสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน
    """

    @staticmethod
    def update_member_info(user_id, validated_data, language='th'):
        """
        อัปเดตข้อมูลสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน

        Args:
            user_id (int): ID ของ Member ที่ต้องการอัปเดต
            validated_data (dict): ข้อมูลที่ผ่านการตรวจสอบแล้วจาก serializer
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: Standard response format
        """
        logger.info(f"Starting member info update for user_id: {user_id}")

        try:
            from django.db import transaction

            with transaction.atomic():
                # ตรวจสอบว่า Member มีอยู่จริงหรือไม่
                try:
                    member = TcdAppMember.objects.get(id=user_id)
                    logger.info(f"Found member to update: {member.username}")
                except TcdAppMember.DoesNotExist:
                    logger.error(f"Member not found with id: {user_id}")
                    return service_error_response(
                        error_code=1000,  # User not found
                        language=language
                    )

                # ตรวจสอบสถานะ member
                if not MemberStatus.is_active(member.status):
                    logger.error(f"Member account is not active: {member.username}, status: {member.status}")
                    error_code = 1002 if member.status == 'L' else 1003  # Account locked or inactive
                    return service_error_response(
                        error_code=error_code,
                        language=language
                    )

                # ตรวจสอบรหัสผ่านปัจจุบัน
                current_password = validated_data.get('current_password')
                if not verify_password(current_password, member.password):
                    logger.error(f"Current password verification failed for member: {member.username}")
                    return service_error_response(
                        error_code=1007,  # Invalid credentials
                        language=language
                    )

                # เก็บข้อมูลเดิมสำหรับ log
                old_data = {
                    'name': member.name,
                    'first_name': member.first_name,
                    'last_name': member.last_name,
                    'email': member.email,
                    'phone': member.phone,
                    'identity_card_no': member.identity_card_no,
                    'website': member.website,
                    'fb_id': member.fb_id,
                    'google_id': member.google_id,
                    'apple_id': member.apple_id,
                    'is_notification': member.is_notification,
                    'lang': member.lang,
                }

                # อัปเดตข้อมูลที่ส่งมา (ยกเว้น current_password)
                updated_fields = []

                # รายการ foreign key fields ที่อนุญาต
                allowed_fk_fields = {
                    'app_mas_member_type_id': 'app_mas_member_type',
                    'app_mas_government_sector_id': 'app_mas_government_sector',
                    'app_mas_ministry_id': 'app_mas_ministry',
                    'app_mas_department_id': 'app_mas_department'
                }

                # รายการ field ปกติที่อนุญาต
                allowed_regular_fields = [
                    'name', 'first_name', 'last_name', 'email', 'phone',
                    'identity_card_no', 'website', 'fb_id', 'google_id', 'apple_id',
                    'app_mas_government_sector_other', 'app_mas_ministry_other',
                    'app_mas_department_other', 'is_notification', 'lang'
                ]

                for field, value in validated_data.items():
                    if field == 'current_password':
                        continue  # ข้ามการอัปเดต current_password

                    # จัดการ foreign key fields
                    if field in allowed_fk_fields:
                        field_name = allowed_fk_fields[field]
                        try:
                            if value is not None:
                                setattr(member, field, value)  # ตั้งค่า _id field โดยตรง
                            else:
                                setattr(member, field, None)
                            updated_fields.append(field_name)
                        except Exception as fk_error:
                            logger.warning(f"Error updating foreign key field {field}: {str(fk_error)}")

                    # จัดการ field ปกติ
                    elif field in allowed_regular_fields:
                        try:
                            old_value = getattr(member, field, None)
                            if old_value != value:
                                setattr(member, field, value)
                                updated_fields.append(field)
                        except Exception as field_error:
                            logger.warning(f"Error updating field {field}: {str(field_error)}")
                    else:
                        logger.warning(f"Field {field} is not allowed for update")

                # บันทึกการเปลี่ยนแปลง
                if updated_fields:
                    member.save()
                    logger.info(f"Member info updated successfully for: {member.username}, updated fields: {updated_fields}")
                else:
                    logger.info(f"No changes detected for member: {member.username}")

                # เตรียมข้อมูลส่งกลับ
                updated_member_data = MemberAuthService.get_member_profile(member)

                return service_success_response(
                    data={
                        'message': 'อัปเดตข้อมูลสมาชิกสำเร็จ' if language == 'th' else 'Member info updated successfully',
                        'member': updated_member_data,
                        'updated_fields': updated_fields
                    },
                    language=language
                )

        except Exception as e:
            logger.error(f"Unexpected error during member info update: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Internal server error
                language=language
            )


class UpdateMemberLangService:
    """
    Service สำหรับการอัปเดตภาษาของสมาชิก Member โดยไม่ต้องตรวจสอบรหัสผ่าน
    """

    @staticmethod
    def update_member_lang(user_id, lang, language='th'):
        """
        อัปเดตภาษาของสมาชิก Member

        Args:
            user_id (int): ID ของ Member ที่ต้องการอัปเดต
            lang (str): ภาษาใหม่ ('th' หรือ 'en')
            language (str): ภาษาของ response ('th' หรือ 'en')

        Returns:
            dict: Standard response format
        """
        logger.info(f"Starting member language update for user_id: {user_id}, new lang: {lang}")

        try:
            from django.db import transaction

            with transaction.atomic():
                # ตรวจสอบว่า Member มีอยู่จริงหรือไม่
                try:
                    member = TcdAppMember.objects.get(id=user_id)
                    logger.info(f"Found member to update: {member.username}")
                except TcdAppMember.DoesNotExist:
                    logger.error(f"Member not found with id: {user_id}")
                    return service_error_response(
                        error_code=1000,  # User not found
                        language=language
                    )

                # ตรวจสอบสถานะ member
                if not MemberStatus.is_active(member.status):
                    logger.error(f"Member account is not active: {member.username}, status: {member.status}")
                    error_code = 1002 if member.status == 'L' else 1003  # Account locked or inactive
                    return service_error_response(
                        error_code=error_code,
                        language=language
                    )

                # เก็บข้อมูลเดิมสำหรับ log
                old_lang = member.lang

                # ตรวจสอบว่าภาษาใหม่แตกต่างจากเดิมหรือไม่
                if old_lang == lang:
                    logger.info(f"No language change needed for member: {member.username}, current lang: {old_lang}")
                    # เตรียมข้อมูลส่งกลับ
                    updated_member_data = MemberAuthService.get_member_profile(member)
                    
                    return service_success_response(
                        data={
                            'message': 'ไม่มีการเปลี่ยนแปลงภาษา' if language == 'th' else 'No language change needed',
                            'member': updated_member_data,
                            'updated_fields': []
                        },
                        language=language
                    )

                # อัปเดตภาษา
                member.lang = lang
                member.save()
                
                logger.info(f"Member language updated successfully for: {member.username}, old lang: {old_lang}, new lang: {lang}")

                # เตรียมข้อมูลส่งกลับ
                updated_member_data = MemberAuthService.get_member_profile(member)

                return service_success_response(
                    data={
                        'message': 'อัปเดตภาษาสำเร็จ' if language == 'th' else 'Language updated successfully',
                        'member': updated_member_data,
                        'updated_fields': ['lang'],
                        'old_lang': old_lang,
                        'new_lang': lang
                    },
                    language=language
                )

        except Exception as e:
            logger.error(f"Unexpected error during member language update: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Internal server error
                language=language
            )


class UpdateConsultantLangService:
    """
    Service สำหรับการอัปเดตภาษาของสมาชิก Member โดยไม่ต้องตรวจสอบรหัสผ่าน
    """

    @staticmethod
    def update_consultant_lang(user_id, lang, language='th'):
        """
        อัปเดตภาษาของสมาชิก Member

        Args:
            user_id (int): ID ของ Member ที่ต้องการอัปเดต
            lang (str): ภาษาใหม่ ('th' หรือ 'en')
            language (str): ภาษาของ response ('th' หรือ 'en')

        Returns:
            dict: Standard response format
        """
        logger.info(f"Starting member language update for user_id: {user_id}, new lang: {lang}")

        try:
            from django.db import transaction
            from authentication.models import TcdUserConsult

            with transaction.atomic():
                # ตรวจสอบว่า Member มีอยู่จริงหรือไม่
                try:
                    consultant = TcdUserConsult.objects.get(id=user_id)
                    logger.info(f"Found consultant to update: {consultant.username}")
                except TcdUserConsult.DoesNotExist:
                    logger.error(f"Member not found with id: {user_id}")
                    return service_error_response(
                        error_code=1000,  # User not found
                        language=language
                    )

                # เก็บข้อมูลเดิมสำหรับ log
                old_lang = consultant.lang

                # ตรวจสอบว่าภาษาใหม่แตกต่างจากเดิมหรือไม่
                if old_lang == lang:
                    logger.info(f"No language change needed for consultant: {consultant.username}, current lang: {old_lang}")
                    # เตรียมข้อมูลส่งกลับ
                    updated_consultant_data = ConsultantAuthService.get_consultant_profile(consultant)
                    
                    return service_success_response(
                        data={
                            'message': 'ไม่มีการเปลี่ยนแปลงภาษา' if language == 'th' else 'No language change needed',
                            'consultant': updated_consultant_data,
                            'updated_fields': []
                        },
                        language=language
                    )

                # อัปเดตภาษา
                consultant.lang = lang
                consultant.save()
                
                logger.info(f"Consultant language updated successfully for: {consultant.username}, old lang: {old_lang}, new lang: {lang}")

                # เตรียมข้อมูลส่งกลับ
                updated_consultant_data = ConsultantAuthService.get_consultant_profile(consultant)

                return service_success_response(
                    data={
                        'message': 'อัปเดตภาษาสำเร็จ' if language == 'th' else 'Language updated successfully',
                        'consultant': updated_consultant_data,
                        'updated_fields': ['lang'],
                        'old_lang': old_lang,
                        'new_lang': lang
                    },
                    language=language
                )

        except Exception as e:
            logger.error(f"Unexpected error during consultant language update: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Internal server error
                language=language
            )


class StaffAuthService:
    """
    Staff Authentication Service for TcdUsers (Staff) model
    Handles JWT token generation and authentication for staff users
    """

    @staticmethod
    def authenticate(request, username, password):
        """
        ตรวจสอบการเข้าสู่ระบบของ Staff
        
        Args:
            request: Django request object
            username: ชื่อผู้ใช้
            password: รหัสผ่าน
            
        Returns:
            dict: Standard response format
        """
        # Get language from request
        language = get_language_from_request(request)
        
        logger.info(f"Starting authentication for staff: {username}")
        
        try:
            # Step 1: ตรวจสอบการเข้าสู่ระบบ
            logger.info("Step 1: Authenticating staff credentials")
            auth_result = StaffAuthService._authenticate_credentials(username, password, language)
            if not auth_result['success']:
                logger.error(f"Staff authentication failed: {auth_result.get('error_code')}")
                
                # บันทึก action log เมื่อเกิด error_code 1001
                if auth_result.get('error_code') == 1001:
                    ActionLogService.log_failed_login_attempt(request, username, 1001, language)
                
                return service_error_response(
                    error_code=auth_result['error_code'],
                    language=language
                )
            
            staff = auth_result['staff']
            logger.info(f"Staff authentication successful: {username}")
            
            # Step 2: Generate tokens
            tokens = StaffAuthService.generate_tokens(staff)

            # Step 2.1: Implement single-session security policy
            logger.info("Implementing single-session security policy for staff")
            try:
                from authentication.services.single_session_service import SingleSessionService

                session_result = SingleSessionService.implement_single_session_login(
                    user_id=staff.id,
                    user_type='staff',
                    tokens=tokens
                )

                if not session_result['success']:
                    logger.error(f"Single-session implementation failed: {session_result['error']}")
                    return service_error_response(
                        error_code=1005,  # Invalid token / Token generation failed
                        language=language
                    )

                logger.info(f"Single-session policy implemented successfully for staff: {username}")

            except Exception as e:
                logger.error(f"Single-session implementation error: {str(e)}")
                return service_error_response(
                    error_code=1005,  # Invalid token / Token generation failed
                    language=language
                )

            # Step 3: Get profile
            user_profile = StaffAuthService.get_staff_profile(staff)
            
            # # Step 4: Update last login
            # staff.lastlogin = timezone.now()
            # staff.save()
            
            # # Step 5: Save login record
            # client_ip = get_client_ip(request)
            # record_result = save_login_record(
            #     user_id=staff.id,
            #     username=username,
            #     user_type='staff',
            #     ip_address=client_ip
            # )
            
            # if not record_result['success']:
            #     logger.warning("Failed to save login record for staff")
            
            # บันทึก app logs เมื่อ login สำเร็จ
            # ActionLogService.log_successful_login(request, staff, 'staff', language)
            
            # เสร็จสิ้นการเข้าสู่ระบบ
            logger.info(f"authentication completed successfully for staff: {username}")
            
            # Prepare response data
            response_data = {
                'user': user_profile,
                'tokens': tokens,
                'session_info': {
                    'login_time': timezone.now().isoformat(),
                    'user_type': 'staff'
                }
            }
            
            return service_success_response(
                data=response_data,
                language=language
            )
            
        except Exception as e:
            logger.error(f"Unexpected authentication error for staff {username}: {str(e)}")
            return service_error_response(
                error_code=5000,  # System error occurred
                language=language
            )
    
    @staticmethod
    def _authenticate_credentials(username, password, language='th'):
        """
        ตรวจสอบการเข้าสู่ระบบของ Staff (Internal method)
        
        Args:
            username: ชื่อผู้ใช้
            password: รหัสผ่าน
            language: ภาษา
            
        Returns:
            dict: Standard response format
        """
        try:
            staff = TcdUsers.objects.get(username=username)
            
            # ตรวจสอบรหัสผ่าน
            if verify_password(password, staff.password):
                return {
                    'success': True,
                    'staff': staff
                }
            else:
                return {
                    'success': False,
                    'error_code': 1001  # Invalid username or password
                }
                
        except TcdUsers.DoesNotExist:
            logger.warning(f"Staff not found: {username}")
            return {
                'success': False,
                'error_code': 1001  # Invalid username or password (don't reveal user doesn't exist)
            }
        except Exception as e:
            logger.error(f"Database error during staff credential authentication: {str(e)}")
            return {
                'success': False,
                'error_code': 3000  # Database error occurred
            }

    @staticmethod
    def generate_tokens(staff_user):
        """
        สร้าง JWT tokens สำหรับ Staff User (TcdUsers)

        Args:
            staff_user: TcdUsers object

        Returns:
            dict: JWT tokens
        """
        # Import NoBlacklistRefreshToken to avoid database blacklist operations
        from authentication.views import NoBlacklistRefreshToken

        refresh = NoBlacklistRefreshToken()

        # เพิ่มข้อมูล custom ใน token
        refresh['user_id'] = int(staff_user.id) if staff_user.id else None
        refresh['username'] = str(staff_user.username) if staff_user.username else None
        refresh['user_type'] = 'staff'
        refresh['email'] = str(staff_user.email) if staff_user.email else None
        refresh['first_name'] = str(staff_user.firstname) if staff_user.firstname else None
        refresh['last_name'] = str(staff_user.lastname) if staff_user.lastname else None

        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }

    @staticmethod
    def get_staff_profile(staff_user):
        """
        ดึงข้อมูลโปรไฟล์ของ Staff User

        Args:
            staff_user: TcdUsers object

        Returns:
            dict: Staff profile data
        """
        return {
            'id': staff_user.id,
            'username': staff_user.username,
            'email': staff_user.email,
            'first_name': staff_user.firstname,
            'last_name': staff_user.lastname,
            'phone': staff_user.phone,
            'position': staff_user.position,
            'user_type': 'staff',
            'is_open': staff_user.is_open,
            'last_login': staff_user.lastlogin.isoformat() if staff_user.lastlogin else None,
        }

    @staticmethod
    def generate_test_token(user_id, username):
        """
        สร้าง JWT token สำหรับการทดสอบ

        Args:
            user_id: ID ของ staff user
            username: Username ของ staff user

        Returns:
            dict: JWT tokens หรือ None ถ้าไม่พบ user
        """
        try:
            staff_user = TcdUsers.objects.get(id=user_id, username=username)
            return StaffAuthService.generate_tokens(staff_user)
        except TcdUsers.DoesNotExist:
            logger.error(f"Staff user not found: id={user_id}, username={username}")
            return None
        except Exception as e:
            logger.error(f"Error generating test token: {str(e)}")
            return None


class UpdateTokenAppService:
    """
    Service สำหรับอัปเดต token_app ของทั้ง TcdAppMember และ TcdUserConsult
    ตรวจสอบ user_type เพื่อเลือก model ที่ถูกต้อง
    """
    
    @staticmethod
    def update_token_app(user_id, user_type, token_app, language='th'):
        """
        อัปเดต token_app ของผู้ใช้ตาม user_type
        
        Args:
            user_id (int): ID ของผู้ใช้ที่ต้องการอัปเดต (ดึงจาก JWT token)
            user_type (str): ประเภทผู้ใช้ ('member' หรือ 'consultant')
            token_app (str): token_app ใหม่
            language (str): ภาษา ('th' หรือ 'en')
            
        Returns:
            dict: Standard response format
        """
        try:
            # ตรวจสอบว่า user_id ไม่เป็นค่าว่าง
            if not user_id:
                return service_error_response(
                    error_code=1001,  # ข้อมูลไม่ครบถ้วน
                    language=language
                )
            
            # ตรวจสอบ user_type
            if user_type not in ['member', 'consultant']:
                return service_error_response(
                    error_code=2001,  # ข้อมูลไม่ถูกต้อง
                    language=language
                )
            
            # ลบ token_app ที่ซ้ำกับ token_app ที่มีอยู่
            member = TcdAppMember.objects.filter(token_app=token_app)
            consultant = TcdUserConsult.objects.filter(token_app=token_app)
            if member.exists():
                member.update(token_app=None)
            if consultant.exists():
                consultant.update(token_app=None)
            
            # อัปเดตตาม user_type
            if user_type == 'member':
                try:
                    member = TcdAppMember.objects.get(id=user_id)
                    member.token_app = token_app
                    member.save(update_fields=['token_app'])
                    
                    logger.info(f"Successfully updated token_app for member ID: {user_id}")
                    
                    return service_success_response(
                        data={
                            'user_id': user_id,
                            'user_type': user_type,
                            'message': 'อัปเดต token_app สำเร็จ'
                        },
                        language=language
                    )
                    
                except TcdAppMember.DoesNotExist:
                    return service_error_response(
                        error_code=1002,  # ไม่พบข้อมูล
                        language=language
                    )
                    
            elif user_type == 'consultant':
                try:
                    consultant = TcdUserConsult.objects.get(id=user_id)
                    consultant.token_app = token_app
                    consultant.save(update_fields=['token_app'])
                    
                    logger.info(f"Successfully updated token_app for consultant ID: {user_id}")
                    
                    return service_success_response(
                        data={
                            'user_id': user_id,
                            'user_type': user_type,
                            'message': 'อัปเดต token_app สำเร็จ'
                        },
                        language=language
                    )
                    
                except TcdUserConsult.DoesNotExist:
                    return service_error_response(
                        error_code=1002,  # ไม่พบข้อมูล
                        language=language
                    )
            
        except Exception as e:
            logger.error(f"Error updating token_app for {user_type} ID {user_id}: {str(e)}")
            return service_error_response(
                error_code=3000,  # เกิดข้อผิดพลาดในฐานข้อมูล
                language=language
            )


class AppUsageTrackingService:
    """
    Service สำหรับการติดตามการใช้งานแอปพลิเคชันรายวัน
    บันทึกการเปิดแอปครั้งแรกของแต่ละ IP address ในแต่ละวัน
    """

    @staticmethod
    def track_daily_app_usage(request, language='th'):
        """
        ติดตามการใช้งานแอปพลิเคชันรายวัน
        ตรวจสอบว่า IP address นี้ได้ถูกบันทึกในวันนี้แล้วหรือไม่
        หากยังไม่ได้บันทึก จะสร้างรายการใหม่ใน app_statistic table

        Args:
            request: Django request object (สำหรับดึง IP address)
            language (str): ภาษา ('th' หรือ 'en')

        Returns:
            dict: Standard response format
        """
        logger.info("Starting daily app usage tracking")

        try:
            # ดึง IP address จาก request
            client_ip = get_client_ip(request)
            if not client_ip:
                logger.warning("Could not extract IP address from request")
                return service_error_response(
                    error_code=2000,  # Invalid data provided
                    language=language
                )

            logger.info(f"Tracking app usage for IP: {client_ip}")

            today = date.today()

            # ใช้ Django ORM แทน SQL query เดิม
            # เทียบเท่ากับ: SELECT COUNT(1) FROM app_statistic
            # WHERE type = 'A' AND ip = <ip_address> AND DATE(date) = <today>
            existing_count = TcdAppStatistic.objects.filter(
                type='A',
                ip=client_ip,
                date__date=today
            ).count()

            logger.info(f"Found {existing_count} existing records for IP {client_ip} on {today}")

            # ถ้ามีการบันทึกแล้วในวันนี้ ไม่ต้องบันทึกซ้ำ
            if existing_count > 0:
                logger.info(f"IP {client_ip} already tracked today, skipping insert")
                return service_success_response(
                    data={
                        'tracked': False,
                        'reason': 'already_tracked_today',
                        'ip_address': client_ip,
                        'date': today.isoformat(),
                        'message': 'การใช้งานแอปของ IP นี้ได้ถูกบันทึกในวันนี้แล้ว' if language == 'th' else 'App usage for this IP has already been tracked today'
                    },
                    language=language
                )

            # บันทึกการใช้งานแอปใหม่
            app_statistic = TcdAppStatistic.objects.create(
                type='A',  # App usage
                ip=client_ip,
                date=timezone.now()
            )

            logger.info(f"Successfully tracked new app usage: ID={app_statistic.id}, IP={client_ip}, Date={app_statistic.date}")

            return service_success_response(
                data={
                    'tracked': True,
                    'record_id': app_statistic.id,
                    'ip_address': client_ip,
                    'date': app_statistic.date.isoformat(),
                    'message': 'บันทึกการใช้งานแอปสำเร็จ' if language == 'th' else 'App usage tracked successfully'
                },
                language=language
            )

        except Exception as e:
            logger.error(f"Error tracking daily app usage: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(
                error_code=3000,  # Database error
                language=language
            )

