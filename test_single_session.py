#!/usr/bin/env python
"""
Test script for single-session JWT token implementation.
This script tests the single-session security policy to ensure only one active session per user.
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MCDC.settings')
django.setup()

from django.db import connection
from authentication.services.single_session_service import SingleSessionService
from authentication.models import TcdAppMember
from authentication.services import MemberAuthService

class SingleSessionTester:
    """Test class for single-session functionality"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"  # Adjust as needed
        self.api_key = "mcdc-api-key-2024"  # Adjust as needed
        self.test_user = {
            "username": "test_single_session",
            "password": "test123456",
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "SingleSession"
        }
        
    def setup_test_user(self):
        """Create or get test user for testing"""
        try:
            # Try to get existing user
            member = TcdAppMember.objects.get(username=self.test_user["username"])
            print(f"✓ Using existing test user: {member.username}")
            return member
        except TcdAppMember.DoesNotExist:
            # Create new test user
            print("Creating new test user...")
            from authentication.utils import md5_hash
            from django.utils import timezone
            
            member = TcdAppMember.objects.create(
                username=self.test_user["username"],
                email=self.test_user["email"],
                password=md5_hash(self.test_user["password"]),
                first_name=self.test_user["first_name"],
                last_name=self.test_user["last_name"],
                status=1,  # Active
                lang='th',
                create_date=timezone.now(),
                checklogin=0,
                is_notification='Y'
            )
            print(f"✓ Created test user: {member.username}")
            return member
    
    def test_token_storage_and_verification(self):
        """Test token storage and verification functionality"""
        print("\n=== Testing Token Storage and Verification ===")
        
        member = self.setup_test_user()
        
        # Generate tokens
        tokens = MemberAuthService.generate_tokens(member)
        print(f"✓ Generated tokens for user {member.username}")
        
        # Test single-session implementation
        result = SingleSessionService.implement_single_session_login(
            user_id=member.id,
            user_type='member',
            tokens=tokens
        )
        
        if result['success']:
            print("✓ Single-session login implementation successful")
        else:
            print(f"✗ Single-session login implementation failed: {result['error']}")
            return False
        
        # Test token verification
        access_valid = SingleSessionService.verify_token_exists(tokens['access'])
        refresh_valid = SingleSessionService.verify_token_exists(tokens['refresh'])
        
        if access_valid and refresh_valid:
            print("✓ Token verification successful - both tokens exist in outstanding tokens")
        else:
            print(f"✗ Token verification failed - access: {access_valid}, refresh: {refresh_valid}")
            return False
        
        return True
    
    def test_single_session_policy(self):
        """Test that only one session can be active at a time"""
        print("\n=== Testing Single-Session Policy ===")
        
        member = self.setup_test_user()
        
        # Generate first set of tokens
        tokens1 = MemberAuthService.generate_tokens(member)
        result1 = SingleSessionService.implement_single_session_login(
            user_id=member.id,
            user_type='member',
            tokens=tokens1
        )
        
        if not result1['success']:
            print(f"✗ Failed to create first session: {result1['error']}")
            return False
        
        print("✓ First session created successfully")
        
        # Verify first tokens are valid
        if not SingleSessionService.verify_token_exists(tokens1['access']):
            print("✗ First session tokens should be valid")
            return False
        
        print("✓ First session tokens are valid")
        
        # Generate second set of tokens (simulating login from another device)
        tokens2 = MemberAuthService.generate_tokens(member)
        result2 = SingleSessionService.implement_single_session_login(
            user_id=member.id,
            user_type='member',
            tokens=tokens2
        )
        
        if not result2['success']:
            print(f"✗ Failed to create second session: {result2['error']}")
            return False
        
        print("✓ Second session created successfully")
        
        # Verify second tokens are valid
        if not SingleSessionService.verify_token_exists(tokens2['access']):
            print("✗ Second session tokens should be valid")
            return False
        
        print("✓ Second session tokens are valid")
        
        # Verify first tokens are now invalid (blacklisted)
        if SingleSessionService.verify_token_exists(tokens1['access']):
            print("✗ First session tokens should be invalid after second login")
            return False
        
        print("✓ First session tokens are now invalid (single-session policy working)")
        
        return True
    
    def test_database_state(self):
        """Test database state after single-session operations"""
        print("\n=== Testing Database State ===")
        
        member = self.setup_test_user()
        
        with connection.cursor() as cursor:
            # Count active tokens for user
            cursor.execute("""
                SELECT COUNT(*)
                FROM tcd_outstanding_tokens ot
                LEFT JOIN tcd_blacklisted_tokens bt ON ot.id = bt.token_id
                WHERE ot.user_id = %s 
                  AND ot.user_type = 'member'
                  AND ot.expires_at > GETUTCDATE()
                  AND bt.id IS NULL
            """, [member.id])
            
            active_count = cursor.fetchone()[0]
            
            # Count blacklisted tokens for user
            cursor.execute("""
                SELECT COUNT(*)
                FROM tcd_outstanding_tokens ot
                INNER JOIN tcd_blacklisted_tokens bt ON ot.id = bt.token_id
                WHERE ot.user_id = %s 
                  AND ot.user_type = 'member'
            """, [member.id])
            
            blacklisted_count = cursor.fetchone()[0]
            
            print(f"✓ User {member.username} has {active_count} active tokens and {blacklisted_count} blacklisted tokens")
            
            # For single-session policy, there should be exactly 2 active tokens (access + refresh)
            if active_count == 2:
                print("✓ Correct number of active tokens (2: access + refresh)")
                return True
            else:
                print(f"✗ Expected 2 active tokens, found {active_count}")
                return False
    
    def cleanup_test_user(self):
        """Clean up test user and related data"""
        print("\n=== Cleaning Up Test Data ===")
        
        try:
            member = TcdAppMember.objects.get(username=self.test_user["username"])
            
            # Clean up tokens
            with connection.cursor() as cursor:
                cursor.execute("""
                    DELETE bt FROM tcd_blacklisted_tokens bt
                    INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
                    WHERE ot.user_id = %s AND ot.user_type = 'member'
                """, [member.id])
                
                cursor.execute("""
                    DELETE FROM tcd_outstanding_tokens
                    WHERE user_id = %s AND user_type = 'member'
                """, [member.id])
            
            # Delete member
            member.delete()
            print(f"✓ Cleaned up test user: {self.test_user['username']}")
            
        except TcdAppMember.DoesNotExist:
            print("✓ No test user to clean up")
    
    def run_all_tests(self):
        """Run all single-session tests"""
        print("Starting Single-Session JWT Token Tests")
        print("=" * 50)
        
        tests = [
            ("Token Storage and Verification", self.test_token_storage_and_verification),
            ("Single-Session Policy", self.test_single_session_policy),
            ("Database State", self.test_database_state),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    print(f"✓ {test_name}: PASSED")
                    passed += 1
                else:
                    print(f"✗ {test_name}: FAILED")
            except Exception as e:
                print(f"✗ {test_name}: ERROR - {str(e)}")
        
        print("\n" + "=" * 50)
        print(f"Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Single-session implementation is working correctly.")
        else:
            print("❌ Some tests failed. Please check the implementation.")
        
        # Cleanup
        self.cleanup_test_user()
        
        return passed == total

if __name__ == "__main__":
    tester = SingleSessionTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
