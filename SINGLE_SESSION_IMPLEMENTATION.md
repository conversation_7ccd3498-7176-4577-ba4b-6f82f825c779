# Single-Session JWT Token Implementation

## Overview

This implementation adds a single-session security policy to the JWT authentication system, ensuring that only one active session per user can exist at any given time. When a user logs in from a new device, all existing tokens for that user are automatically invalidated.

## Key Components

### 1. SingleSessionService (`authentication/services/single_session_service.py`)

A new service class that handles single-session security policy:

- **`invalidate_existing_user_tokens(user_id, user_type)`**: Blacklists all existing active tokens for a user
- **`store_token_in_outstanding(token_str, user_id, user_type)`**: Stores JWT tokens in `tcd_outstanding_tokens` with proper hashing
- **`verify_token_exists(token_str)`**: Verifies that a token exists in outstanding tokens and is not blacklisted
- **`implement_single_session_login(user_id, user_type, tokens)`**: Main method that implements the complete single-session flow

### 2. Updated Authentication Services

Modified the following authentication services to use single-session logic:

- **MemberAuthService**: Updated `authenticate()` method to implement single-session policy after token generation
- **ConsultantAuthService**: Updated `_process_authentication_data()` method
- **StaffAuthService**: Updated `authenticate()` method
- **MemberAuthService**: Updated `register_member_full()` method for registration process

### 3. Updated Authentication Backend (`authentication/backends.py`)

Modified `CustomJWTAuthentication` class:

- Added `check_token_outstanding()` method to verify tokens exist in outstanding tokens table
- Integrated the check into the `get_validated_token()` flow
- Tokens must exist in `tcd_outstanding_tokens` and not be blacklisted to be considered valid

## How It Works

### Login Process

1. **User Authentication**: Credentials are validated
2. **Token Generation**: New JWT tokens (access + refresh) are generated
3. **Existing Token Invalidation**: All existing tokens for the user are blacklisted
4. **New Token Storage**: New tokens are stored in `tcd_outstanding_tokens` with hashed values
5. **Response**: User receives new tokens and can access the system

### Token Verification Process

1. **Token Extraction**: JWT token is extracted from Authorization header
2. **Standard Validation**: Token signature and expiration are validated
3. **Blacklist Check**: Token is checked against blacklist
4. **Outstanding Token Check**: Token must exist in `tcd_outstanding_tokens` and not be blacklisted
5. **User Authentication**: If all checks pass, user is authenticated

### Database Schema

The implementation uses existing tables:

- **`tcd_outstanding_tokens`**: Stores all issued tokens with hashed values
- **`tcd_blacklisted_tokens`**: Stores blacklisted (invalidated) tokens

## Security Benefits

1. **Single-Session Policy**: Only one active session per user prevents unauthorized concurrent access
2. **Token Hashing**: Tokens are stored as SHA-256 hashes for security
3. **Automatic Invalidation**: Previous sessions are automatically invalidated on new login
4. **Centralized Control**: All token validation goes through the outstanding tokens table

## Testing the Implementation

### Manual Testing Steps

1. **Test Single-Session Policy**:
   ```bash
   # Login from first device/browser
   curl -X POST http://localhost:8000/auth/member/login/ \
     -H "Content-Type: application/json" \
     -H "X-API-Key: mcdc-api-key-2024" \
     -d '{"username": "testuser", "password": "testpass"}'
   
   # Save the access token from response
   TOKEN1="<access_token_from_response>"
   
   # Test API access with first token
   curl -X GET http://localhost:8000/auth/profile/ \
     -H "Authorization: Bearer $TOKEN1" \
     -H "X-API-Key: mcdc-api-key-2024"
   
   # Login from second device/browser (same user)
   curl -X POST http://localhost:8000/auth/member/login/ \
     -H "Content-Type: application/json" \
     -H "X-API-Key: mcdc-api-key-2024" \
     -d '{"username": "testuser", "password": "testpass"}'
   
   # Save the new access token
   TOKEN2="<new_access_token_from_response>"
   
   # Test that first token is now invalid
   curl -X GET http://localhost:8000/auth/profile/ \
     -H "Authorization: Bearer $TOKEN1" \
     -H "X-API-Key: mcdc-api-key-2024"
   # Should return 401 Unauthorized
   
   # Test that second token is valid
   curl -X GET http://localhost:8000/auth/profile/ \
     -H "Authorization: Bearer $TOKEN2" \
     -H "X-API-Key: mcdc-api-key-2024"
   # Should return user profile
   ```

2. **Database Verification**:
   ```sql
   -- Check outstanding tokens for a user
   SELECT ot.id, ot.jti, ot.created_at, ot.expires_at, 
          CASE WHEN bt.id IS NOT NULL THEN 'Blacklisted' ELSE 'Active' END as status
   FROM tcd_outstanding_tokens ot
   LEFT JOIN tcd_blacklisted_tokens bt ON ot.id = bt.token_id
   WHERE ot.user_id = <user_id> AND ot.user_type = 'member'
   ORDER BY ot.created_at DESC;
   
   -- Should show only 2 active tokens (access + refresh) for the latest login
   -- All previous tokens should be blacklisted
   ```

### Expected Behavior

- ✅ User can login successfully and receive tokens
- ✅ User can access protected endpoints with valid tokens
- ✅ When user logs in from another device, previous tokens become invalid
- ✅ Only the latest login session remains active
- ✅ Database shows proper token management (active vs blacklisted)

## Error Handling

The implementation includes comprehensive error handling:

- **Token Storage Failures**: Returns appropriate error codes
- **Database Connection Issues**: Graceful fallback behavior
- **Invalid Tokens**: Clear error messages for debugging
- **Single-Session Failures**: Proper error reporting during login

## Configuration

No additional configuration is required. The implementation uses:

- Existing `tcd_outstanding_tokens` and `tcd_blacklisted_tokens` tables
- Current JWT settings from `SIMPLE_JWT` configuration
- Existing token hashing utilities from `authentication.utils`

## Backward Compatibility

The implementation maintains backward compatibility:

- Existing token refresh mechanisms continue to work
- Current blacklist functionality is preserved
- No changes to API endpoints or response formats
- Existing logout and token revocation features remain functional

## Performance Considerations

- **Database Queries**: Minimal additional queries during authentication
- **Token Hashing**: Efficient SHA-256 hashing for secure storage
- **Index Usage**: Leverages existing database indexes on `tcd_outstanding_tokens`
- **Memory Usage**: No additional memory overhead

## Monitoring and Logging

The implementation includes comprehensive logging:

- Token invalidation events
- Single-session policy enforcement
- Database operation results
- Error conditions and debugging information

All logs use the standard Django logging framework with appropriate log levels.
